import * as cron from 'node-cron';
import { recipeCostFreshnessCronJob } from './recipeCostFreshnessCron';
import { ingredientCostUpdateCronJob } from './ingredientCostUpdateCron';

/**
 * Initialize and start all cron jobs
 */
export const initializeCronJobs = (): void => {
  console.log('Initializing cron jobs...');

  // Recipe Cost Freshness Cron Job
  // Runs based on COST_RECALCULATION_CRON configuration
  const costRecalculationSchedule = global.config.COST_RECALCULATION_CRON || '0 * * * *'; // Default: every hour

  cron.schedule(costRecalculationSchedule, async () => {
    try {
      console.log(`Starting scheduled recipe cost freshness update at ${new Date().toISOString()}`);
      await recipeCostFreshnessCronJob();
    } catch (error) {
      console.error('Recipe cost freshness cron job failed:', error);
    }
  }, {
    scheduled: true,
    timezone: global.config.TIMEZONE || 'UTC'
  });

  console.log(`Recipe cost freshness cron job scheduled: ${costRecalculationSchedule} (${global.config.TIMEZONE || 'UTC'})`);

  // Ingredient Cost Update Cron Job (existing)
  // Runs based on CRON_RENEWAL_TIME configuration
  const renewalSchedule = global.config.CRON_RENEWAL_TIME || '*/10 * * * *'; // Default: every 10 minutes

  cron.schedule(renewalSchedule, async () => {
    try {
      console.log(`Starting scheduled ingredient cost update at ${new Date().toISOString()}`);
      await ingredientCostUpdateCronJob();
    } catch (error) {
      console.error('Ingredient cost update cron job failed:', error);
    }
  }, {
    scheduled: true,
    timezone: global.config.TIMEZONE || 'UTC'
  });

  console.log(`Ingredient cost update cron job scheduled: ${renewalSchedule} (${global.config.TIMEZONE || 'UTC'})`);

  console.log('All cron jobs initialized successfully');
};

/**
 * Stop all cron jobs (useful for graceful shutdown)
 */
export const stopAllCronJobs = (): void => {
  console.log('Stopping all cron jobs...');
  cron.getTasks().forEach((task: any) => {
    task.stop();
  });
  console.log('All cron jobs stopped');
};

export default {
  initializeCronJobs,
  stopAllCronJobs
};
