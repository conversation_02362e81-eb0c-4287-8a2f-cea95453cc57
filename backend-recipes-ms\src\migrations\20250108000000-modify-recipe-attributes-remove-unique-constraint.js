'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('🔄 Starting RecipeAttributes table modification...');

      // Step 1: Create new table with proper structure
      console.log('📋 Creating new table structure...');
      await queryInterface.createTable('mo_recipe_attributes_new', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        recipe_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'mo_recipe',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        attributes_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'mo_food_attributes',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        unit_of_measure: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        unit: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive'),
          allowNull: false,
          defaultValue: 'active',
        },
        organization_id: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        attribute_description: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        may_contain: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
        use_default: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        },
      }, { transaction });
      console.log('✅ New table structure created');

      // Step 2: Copy data from old table to new table
      console.log('📋 Copying data from old table...');
      await queryInterface.sequelize.query(`
        INSERT INTO mo_recipe_attributes_new
        (recipe_id, attributes_id, unit_of_measure, unit, status, organization_id,
         attribute_description, may_contain, use_default, created_by, updated_by, created_at, updated_at)
        SELECT recipe_id, attributes_id, unit_of_measure, unit, status, organization_id,
               attribute_description, may_contain, use_default, created_by, updated_by, created_at, updated_at
        FROM mo_recipe_attributes
      `, { transaction });
      console.log('✅ Data copied successfully');

      // Step 3: Drop old table
      console.log('📋 Dropping old table...');
      await queryInterface.dropTable('mo_recipe_attributes', { transaction });
      console.log('✅ Old table dropped');

      // Step 4: Rename new table to original name
      console.log('📋 Renaming new table...');
      await queryInterface.renameTable('mo_recipe_attributes_new', 'mo_recipe_attributes', { transaction });
      console.log('✅ Table renamed');

      // Step 5: Add performance index
      console.log('📋 Adding performance index...');
      await queryInterface.addIndex('mo_recipe_attributes', ['recipe_id', 'attributes_id'], {
        name: 'idx_recipe_attributes_composite',
        transaction
      });
      console.log('✅ Performance index added');

      await transaction.commit();
      console.log('✅ RecipeAttributes table modification completed successfully!');
      console.log('🎉 HACCP attributes now support unlimited duplicate entries per attribute ID');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error modifying RecipeAttributes table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('🔄 Reverting RecipeAttributes table modification...');

      // Step 1: Create old table structure with composite primary key
      console.log('📋 Creating old table structure...');
      await queryInterface.createTable('mo_recipe_attributes_old', {
        recipe_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'mo_recipe',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        attributes_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'mo_food_attributes',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        unit_of_measure: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        unit: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive'),
          allowNull: false,
          defaultValue: 'active',
        },
        organization_id: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        attribute_description: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        may_contain: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
        use_default: {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false,
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        },
      }, { transaction });

      // Step 2: Copy data back (excluding duplicates due to composite primary key)
      console.log('📋 Copying data back (may lose duplicate entries)...');
      await queryInterface.sequelize.query(`
        INSERT IGNORE INTO mo_recipe_attributes_old
        (recipe_id, attributes_id, unit_of_measure, unit, status, organization_id,
         attribute_description, may_contain, use_default, created_by, updated_by, created_at, updated_at)
        SELECT recipe_id, attributes_id, unit_of_measure, unit, status, organization_id,
               attribute_description, may_contain, use_default, created_by, updated_by, created_at, updated_at
        FROM mo_recipe_attributes
      `, { transaction });

      // Step 3: Drop new table and rename old table
      await queryInterface.dropTable('mo_recipe_attributes', { transaction });
      await queryInterface.renameTable('mo_recipe_attributes_old', 'mo_recipe_attributes', { transaction });

      // Step 4: Add back the unique constraint
      await queryInterface.addIndex('mo_recipe_attributes', ['recipe_id', 'attributes_id', 'may_contain', 'use_default'], {
        unique: true,
        name: 'unique_recipe_attributes_composite',
        transaction
      });

      await transaction.commit();
      console.log('✅ RecipeAttributes table rollback completed');
      console.log('⚠️  Note: Duplicate HACCP entries may have been lost during rollback');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error rolling back RecipeAttributes table:', error);
      throw error;
    }
  }
};
