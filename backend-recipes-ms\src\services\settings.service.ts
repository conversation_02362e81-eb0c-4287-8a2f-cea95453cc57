import { QueryTypes } from "sequelize";
import Settings, { SettingType, SettingCategory } from "../models/Settings";
import { sequelize } from "../models";

interface SettingDefinition {
  key: string;
  defaultValue: any;
  type: SettingType;
  category: SettingCategory;
  description: string;
  isSystem: boolean;
}

const DEFAULT_SETTINGS: SettingDefinition[] = [
  // Private Recipe Visibility Settings
  {
    key: "recipe.highlight_changes",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.RECIPE,
    description:
      "Enable this to show highlighted changes to the assigned team member when viewing the recipe",
    isSystem: false,
  },

  // Public Recipe Settings
  {
    key: "recipe.public_store_enabled",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.RECIPE,
    description:
      "Enable to make public recipe features available. Turning this off hides all public recipe options",
    isSystem: false,
  },

  // Public Recipe Call-To-Action Settings
  {
    key: "recipe.cta_contact_form",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show a basic contact form (Name, Email, Phone, Message)",
    isSystem: false,
  },
  {
    key: "recipe.cta_contact_info",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Display a predefined contact block (Phone, Email, Link)",
    isSystem: false,
  },
  {
    key: "recipe.cta_custom_link",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show a custom CTA with text and an external link",
    isSystem: false,
  },
  {
    key: "recipe.cta_none",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show nothing",
    isSystem: false,
  },

  // Contact Info Fields (for when contact info is selected)
  {
    key: "recipe.contact_info_name",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Contact name",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_phone",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Phone number",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_email",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Email address",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_link",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Link URL",
    isSystem: false,
  },

  // Custom CTA Fields (for when custom CTA is selected)
  {
    key: "recipe.custom_cta_text",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Custom CTA button text",
    isSystem: false,
  },
  {
    key: "recipe.custom_cta_link",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Custom CTA link URL",
    isSystem: false,
  },

  // Recipe Details to Display Publicly (exactly as shown in UI screenshots)
  {
    key: "recipe.display_category",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Category",
    isSystem: false,
  },
  {
    key: "recipe.display_ingredients",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Ingredients",
    isSystem: false,
  },
  {
    key: "recipe.display_nutritional_information",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Nutritional Information",
    isSystem: false,
  },
  {
    key: "recipe.display_allergen_information",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Allergen Information",
    isSystem: false,
  },
  {
    key: "recipe.display_preparation_steps",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Preparation Steps",
    isSystem: false,
  },
  {
    key: "recipe.display_total_time",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Total Time",
    isSystem: false,
  },
  {
    key: "recipe.display_yield_portioning",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Yield & Portioning",
    isSystem: false,
  },
  {
    key: "recipe.display_cost",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Cost",
    isSystem: false,
  },
  {
    key: "recipe.display_dietary_suitability",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Dietary Suitability",
    isSystem: false,
  },
  {
    key: "recipe.display_cuisine_type",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Cuisine Type",
    isSystem: false,
  },
  {
    key: "recipe.display_media",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Media",
    isSystem: false,
  },
  {
    key: "recipe.display_links",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Links",
    isSystem: false,
  },
  {
    key: "recipe.display_scale",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Scale",
    isSystem: false,
  },
  {
    key: "recipe.display_serve_in",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Serve In",
    isSystem: false,
  },
  {
    key: "recipe.display_garnish",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Garnish",
    isSystem: false,
  },
  {
    key: "recipe.display_haccp",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "HACCP",
    isSystem: false,
  },

  // Financial Year Settings
  {
    key: "financial_month",
    defaultValue: 1, // January (1-based month)
    type: SettingType.NUMBER,
    category: SettingCategory.ANALYTICS,
    description:
      "Starting month of the financial year (1=January, 2=February, etc.)",
    isSystem: false,
  },
  // Recipe Organization Name (unique slug per org)
  {
    key: "recipe.organization_name",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.RECIPE,
    description: "Unique organization slug e.g. myhotel_123",
    isSystem: true,
  },
];

/**
 * Parse setting value based on type
 */
const parseSettingValue = (setting: Settings): any => {
  switch (setting.setting_type) {
    case SettingType.BOOLEAN:
      return setting.getBooleanValue();
    case SettingType.NUMBER:
      return setting.getNumberValue();
    case SettingType.JSON:
      return setting.getJsonValue();
    case SettingType.ARRAY:
      return setting.getArrayValue();
    case SettingType.STRING:
    default:
      return setting.getStringValue();
  }
};

/**
 * Serialize setting value for storage
 */
const serializeSettingValue = (value: any, type: SettingType): string => {
  if (type === SettingType.JSON || type === SettingType.ARRAY) {
    return JSON.stringify(value);
  }
  return String(value);
};

/**
 * Get setting value with fallback to default
 */
export const getSetting = async (
  key: string,
  organizationId?: string
): Promise<any> => {
  try {
    const setting = await sequelize.query(
      `SELECT \`value\` FROM nv_settings WHERE \`key\` = :key AND organization_id = :organizationId`,
      {
        replacements: { key, organizationId },
        type: QueryTypes.SELECT,
      }
    );
    return setting[0].value;
  } catch (error) {
    console.warn(`Error fetching setting '${key}':`, error);
  }

  // Return default value if setting doesn't exist or there was an error
  const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
  return defaultSetting ? defaultSetting.defaultValue : null;
};

/**
 * Get all settings for a category
 */
export const getSettingsByCategory = async (
  category: SettingCategory,
  organizationId?: string
): Promise<Record<string, any>> => {
  // Fetch existing settings for the org + category
  let settings = await Settings.findAll({
    where: {
      setting_category: category,
      organization_id: organizationId || null,
    },
  });

  // ------------------------------------------------------------------
  // Auto-seed defaults if *any* setting for this category is missing
  // ------------------------------------------------------------------
  if (organizationId) {
    const missingCount = DEFAULT_SETTINGS.filter(
      (def) =>
        def.category === category &&
        !settings.find((s: Settings) => s.setting_key === def.key)
    ).length;

    if (missingCount > 0) {
      // Seed missing defaults (also generates recipe.organization_name slug)
      await initializeDefaultSettings(organizationId, 1); // 1 = system user
      // Re-query after seeding so we return the fresh DB values
      settings = await Settings.findAll({
        where: {
          setting_category: category,
          organization_id: organizationId || null,
        },
      });
    }
  }

  const result: Record<string, any> = {};

  // Add existing (or now-seeded) settings
  settings.forEach((setting: Settings) => {
    result[setting.setting_key] = parseSettingValue(setting);
  });

  // ------------------------------------------------------------------
  // Ensure organization slug exists and is non-empty
  // ------------------------------------------------------------------
  if (
    organizationId &&
    category === SettingCategory.RECIPE &&
    (!result["recipe.organization_name"] ||
      result["recipe.organization_name"].trim() === "")
  ) {
    try {
      const orgName = await getOrganizationNameById(organizationId);
      if (orgName) {
        const newSlug = await generateUniqueOrganizationSlug(orgName);
        await updateSetting(
          "recipe.organization_name",
          newSlug,
          organizationId,
          1
        );
        result["recipe.organization_name"] = newSlug;
      }
    } catch (err) {
      console.warn("Failed to regenerate organization slug", err);
    }
  }

  // Add any remaining defaults to ensure complete object (in-memory fallback)
  DEFAULT_SETTINGS.filter((def) => def.category === category).forEach((def) => {
    if (!(def.key in result)) {
      result[def.key] = def.defaultValue;
    }
  });

  return result;
};

/**
 * Update or create setting
 */
export const updateSetting = async (
  key: string,
  value: any,
  organizationId?: string,
  userId?: number
): Promise<Settings> => {
  const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
  if (!defaultSetting) {
    throw new Error(`Unknown setting key: ${key}`);
  }

  const serializedValue = serializeSettingValue(value, defaultSetting.type);

  const [setting] = await Settings.upsert({
    setting_key: key,
    setting_value: serializedValue,
    setting_type: defaultSetting.type,
    setting_category: defaultSetting.category,
    setting_description: defaultSetting.description,
    is_system_setting: defaultSetting.isSystem,
    organization_id: organizationId || null,
    created_by: userId || 1,
    updated_by: userId || 1,
  });

  return setting;
};

/**
 * Update multiple settings at once
 */
export const updateSettings = async (
  settings: Record<string, any>,
  organizationId?: string,
  userId?: number
): Promise<void> => {
  const promises = Object.entries(settings).map(([key, value]) =>
    updateSetting(key, value, organizationId, userId)
  );

  await Promise.all(promises);
};

/**
 * Initialize default settings for an organization
 */
export const initializeDefaultSettings = async (
  organizationId: string,
  userId: number
): Promise<void> => {
  const existingSettings = await Settings.findAll({
    where: { organization_id: organizationId },
    attributes: ["setting_key"],
  });

  const existingKeys = existingSettings.map((s: Settings) => s.setting_key);
  const missingSettings = DEFAULT_SETTINGS.filter(
    (def) => !existingKeys.includes(def.key)
  );

  let orgSlug = "";
  try {
    const orgName = await getOrganizationNameById(organizationId);
    if (orgName) {
      orgSlug = await generateUniqueOrganizationSlug(orgName);
    }
  } catch (err) {
    console.warn("Unable to generate organization slug", err);
  }

  if (missingSettings.length > 0) {
    const settingsToCreate = missingSettings.map((def) => ({
      setting_key: def.key,
      setting_value:
        def.key === "recipe.organization_name"
          ? orgSlug
          : serializeSettingValue(def.defaultValue, def.type),
      setting_type: def.type,
      setting_category: def.category,
      setting_description: def.description,
      is_system_setting: def.isSystem,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    await Settings.bulkCreate(settingsToCreate);
  }
};

/**
 * Get setting definitions (for UI generation)
 */
export const getSettingDefinitions = (): SettingDefinition[] => {
  return DEFAULT_SETTINGS;
};

/**
 * Get all settings for an organization (common function for public APIs)
 * Returns both recipe and public settings combined
 */
export const getSettingsByOrganizationId = async (
  organizationId: string
): Promise<Record<string, any>> => {
  try {
    // Get both recipe and public settings for the organization
    const [recipeSettings, publicSettings] = await Promise.all([
      getSettingsByCategory(SettingCategory.RECIPE, organizationId),
      getSettingsByCategory(SettingCategory.PUBLIC, organizationId),
    ]);

    // Combine both settings into a single object
    return {
      ...recipeSettings,
      ...publicSettings,
    };
  } catch (error) {
    console.error("Error in getSettingsByOrganizationId:", error);
    throw error;
  }
};

/**
 * Get structured settings for an organization (for public APIs)
 * Returns settings in the same format as getRecipeConfiguration API
 */
export const getStructuredSettingsByOrganizationId = async (
  organizationId: string
): Promise<Record<string, any>> => {
  try {
    // Get both recipe and public settings for the organization
    const [recipeSettings, publicSettings] = await Promise.all([
      getSettingsByCategory(SettingCategory.RECIPE, organizationId),
      getSettingsByCategory(SettingCategory.PUBLIC, organizationId),
    ]);

    // Structure the settings in the same format as getRecipeConfiguration
    const structuredSettings = {
      // Private Recipe Visibility Settings
      privateRecipeVisibilitySettings: {
        highlightChanges: recipeSettings["recipe.highlight_changes"] ?? false,
      },

      // Public Recipe Settings
      publicRecipeSettings: {
        publicStoreAccess:
          recipeSettings["recipe.public_store_enabled"] || false,
      },

      // Public Recipe Call-To-Action (CTA)
      publicRecipeCallToAction: {
        contactForm: publicSettings["recipe.cta_contact_form"] ?? true,
        contactInfo: {
          enabled: publicSettings["recipe.cta_contact_info"] || false,
          name: publicSettings["recipe.contact_info_name"] || "",
          phone: publicSettings["recipe.contact_info_phone"] || "",
          email: publicSettings["recipe.contact_info_email"] || "",
          link: publicSettings["recipe.contact_info_link"] || "",
        },
        customCtaLink: {
          enabled: publicSettings["recipe.cta_custom_link"] || false,
          text: publicSettings["recipe.custom_cta_text"] || "",
          link: publicSettings["recipe.custom_cta_link"] || "",
        },
        none: publicSettings["recipe.cta_none"] || false,
      },

      // Recipe Details to Display Publicly
      recipeDetailsToDisplayPublicly: {
        category: publicSettings["recipe.display_category"] ?? true,
        ingredients: publicSettings["recipe.display_ingredients"] ?? true,
        nutritionalInformation:
          publicSettings["recipe.display_nutritional_information"] ?? true,
        allergenInformation:
          publicSettings["recipe.display_allergen_information"] ?? true,
        preparationSteps:
          publicSettings["recipe.display_preparation_steps"] ?? true,
        totalTime: publicSettings["recipe.display_total_time"] ?? false,
        yieldPortioning:
          publicSettings["recipe.display_yield_portioning"] ?? false,
        cost: publicSettings["recipe.display_cost"] ?? false,
        dietarySuitability:
          publicSettings["recipe.display_dietary_suitability"] ?? false,
        cuisineType: publicSettings["recipe.display_cuisine_type"] ?? false,
        media: publicSettings["recipe.display_media"] ?? false,
        links: publicSettings["recipe.display_links"] ?? false,
        scale: publicSettings["recipe.display_scale"] ?? false,
        serveIn: publicSettings["recipe.display_serve_in"] ?? false,
        garnish: publicSettings["recipe.display_garnish"] ?? false,
        haccp: publicSettings["recipe.display_haccp"] ?? false,
      },

      // Organization unique slug
      organizationSlug: recipeSettings["recipe.organization_name"] || "",
    };

    return structuredSettings;
  } catch (error) {
    console.error("Error in getStructuredSettingsByOrganizationId:", error);
    throw error;
  }
};

/**
 * Get organization name by ID
 */
export async function getOrganizationNameById(
  organizationId: string
): Promise<string | null> {
  if (!organizationId) return null;
  try {
    const [org]: any = await sequelize.query(
      "SELECT NAME as name FROM `ORG` WHERE id = :orgId LIMIT 1",
      { replacements: { orgId: organizationId }, type: QueryTypes.SELECT }
    );
    return org?.name || null;
  } catch (err) {
    console.error("Error fetching org name", err);
    return null;
  }
}

/** Generate unique slug: orgName(no spaces/lower) + _ + random 3-digit. Ensures uniqueness in settings table */
export async function generateUniqueOrganizationSlug(
  orgName: string
): Promise<string> {
  const base = orgName.replace(/\s+/g, "").toLowerCase();
  let unique = false;
  let slug = "";
  while (!unique) {
    const rand = Math.floor(100 + Math.random() * 900); // 100-999
    slug = `${base}_${rand}`;
    const existing = await Settings.findOne({
      where: { setting_key: "recipe.organization_name", setting_value: slug },
    });
    if (!existing) unique = true;
  }
  return slug;
}

// export async function getAllOrganizationSlugs(): Promise<
//   { organization_id: string | null; slug: string }[]
// > {
//   const rows: any[] = await sequelize.query(
//     "SELECT organization_id, setting_value AS slug FROM mo_recipe_settings WHERE setting_key = :key",
//     {
//       replacements: { key: "recipe.organization_name" },
//       type: QueryTypes.SELECT,
//       logging: false,
//     }
//   );

//   return rows.map((r: any) => ({
//     organization_id: r.organization_id || null,
//     slug: r.slug,
//   }));
// }

export default {
  getSetting,
  getSettingsByCategory,
  updateSetting,
  updateSettings,
  initializeDefaultSettings,
  getSettingDefinitions,
  getSettingsByOrganizationId,
  getStructuredSettingsByOrganizationId,
  getOrganizationNameById,
  generateUniqueOrganizationSlug,
};
