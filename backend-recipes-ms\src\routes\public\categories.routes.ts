import express from "express";
import categoryController from "../../controller/category.controller";
import categoryValidator from "../../validators/category.validator";
import uploadController from "../../controller/upload.controller";

const router = express.Router();

/**
 * @swagger
 * /public/categories:
 *   get:
 *     tags:
 *       - Public Categories
 *     summary: Get all active categories
 *     description: Retrieve all active categories with filtering, searching, and pagination. No authentication required.
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for category name or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: type
 *         in: query
 *         description: Filter by category type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [recipe, ingredient]
 *       - name: categoryUse
 *         in: query
 *         description: Alias for type parameter
 *         required: false
 *         schema:
 *           type: string
 *           enum: [recipe, ingredient]
 *       - name: hasIcon
 *         in: query
 *         description: Filter categories by icon presence
 *         required: false
 *         schema:
 *           type: boolean
 *       - name: sort
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [category_name, created_at, updated_at]
 *           default: category_name
 *       - name: order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Categories fetched successfully"
 *                 count:
 *                   type: integer
 *                   example: 25
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       category_name:
 *                         type: string
 *                         example: "Vegetables"
 *                       category_description:
 *                         type: string
 *                         example: "Fresh vegetables and greens"
 *                       category_type:
 *                         type: string
 *                         enum: [recipe, ingredient]
 *                         example: "ingredient"
 *                       category_status:
 *                         type: string
 *                         enum: [active]
 *                         example: "active"
 *                       category_slug:
 *                         type: string
 *                         example: "vegetables"
 *                       iconUrl:
 *                         type: string
 *                         nullable: true
 *                         example: "https://example.com/icons/vegetables.png"
 *                       hasIcon:
 *                         type: boolean
 *                         example: true
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:30:00Z"
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 10
 *                 total_pages:
 *                   type: integer
 *                   example: 3
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation failed"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Invalid page number"]
 *       429:
 *         description: Too many requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Rate limit exceeded"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get(
  "/list",
  categoryValidator.getCategoriesListValidator(),
  (req: any, res: any, next: any) => {
    // For public API, simulate a user with default access to bypass organization restrictions
    req.user = {
      id: null,
      organization_id: null,
      roles: [{ role_name: "public" }],
    };
    // Force status to 'active' for public API
    req.query.status = "active";
    // Remove any organization filters for public access
    delete req.query.organizationId;
    delete req.query.isSystemCategory;
    next();
  },
  categoryController.getAllCategories
);

/**
 * @swagger
 * /public/categories/upload-default-icons:
 *   post:
 *     tags:
 *       - Public Categories
 *     summary: Upload default icons for categories and allergens
 *     description: Upload default icons for recipe categories, ingredient categories, and allergens. No authentication required.
 *     parameters:
 *       - name: type
 *         in: query
 *         description: Type of icons to upload
 *         required: false
 *         schema:
 *           type: string
 *           enum: [all, recipe-categories, ingredient-categories, allergens]
 *           default: all
 *     responses:
 *       200:
 *         description: Icons uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Default icon upload completed for: all"
 *                 type:
 *                   type: string
 *                   example: "all"
 *                 results:
 *                   type: object
 *                   properties:
 *                     recipeCategories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "Main Course"
 *                           status:
 *                             type: string
 *                             enum: [success, skipped, failed, error]
 *                             example: "success"
 *                           iconId:
 *                             type: integer
 *                             example: 123
 *                           fileName:
 *                             type: string
 *                             example: "Main Courses Stroke Black.png"
 *                           reason:
 *                             type: string
 *                             example: "Already has icon"
 *                     ingredientCategories:
 *                       type: array
 *                       items:
 *                         type: object
 *                     allergens:
 *                       type: array
 *                       items:
 *                         type: object
 *                 summary:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 39
 *                     success:
 *                       type: integer
 *                       example: 35
 *                     skipped:
 *                       type: integer
 *                       example: 4
 *                     failed:
 *                       type: integer
 *                       example: 0
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to upload default icons"
 *                 error:
 *                   type: string
 *                   example: "Database connection error"
 */
router.post("/upload-default-icons", uploadController.uploadDefaultIcons);

export default router;
