import { celebrate, Joi, Segments } from "celebrate";
import { AttributeStatus, AttributeType } from "../models/FoodAttributes";

const createFoodAttributeValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        attribute_title: Joi.string().min(2).max(100).required(),
        attribute_description: Joi.string().allow(null, "").optional(),
        attribute_type: Joi.string()
          .valid(...Object.values(AttributeType))
          .required(),
        attribute_status: Joi.string()
          .valid(...Object.values(AttributeStatus))
          .optional(),
        is_system_attribute: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
  });

const updateFoodAttributeValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        attribute_title: Joi.string().min(2).max(100).optional(),
        attribute_description: Joi.string().allow(null, "").optional(),
        attribute_type: Joi.string()
          .valid(...Object.values(AttributeType))
          .optional(),
        attribute_status: Joi.string()
          .valid(...Object.values(AttributeStatus))
          .optional(),
        is_system_attribute: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().min(1).required(),
    }),
  });

const deleteFoodAttributeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getFoodAttributeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getFoodAttributesListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      search: Joi.string().max(100).optional(),
      status: Joi.string()
        .valid(...Object.values(AttributeStatus))
        .optional(),
      type: Joi.string()
        .valid(...Object.values(AttributeType))
        .optional(),
      sort_by: Joi.string()
        .valid("attribute_title", "created_at", "updated_at")
        .default("created_at"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

// Default export object
export default {
  createFoodAttributeValidator,
  updateFoodAttributeValidator,
  deleteFoodAttributeValidator,
  getFoodAttributeValidator,
  getFoodAttributesListValidator,
};
