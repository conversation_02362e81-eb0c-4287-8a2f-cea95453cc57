import userAuth from "./auth";

const secureDocs = async (req: any, res: any, next: any) => {
  try {
    // Ensure userAuth is awaited if it is an async function or called properly as middleware
    await userAuth(req, res, next);
  } catch (error: any) {
    console.error(error); // Use console.error for errors
    return res.status(401).send({ status: false, message: error.message });
  }
};

export default secureDocs;
