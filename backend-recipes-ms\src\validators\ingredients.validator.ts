import { celebrate, Joi, Segments } from "celebrate";

const createIngredientValidator = () =>
    celebrate({
        [Segments.BODY]: Joi.object().keys({
            ingredient_name: Joi.string().min(2).max(100).trim().required().messages({
                "string.min": "Ingredient name must be at least 2 characters long",
                "string.max": "Ingredient name cannot exceed 100 characters",
                "any.required": "Ingredient name is required",
            }),
            ingredient_description: Joi.string().max(1000).allow(null, "").messages({
                "string.max": "Description cannot exceed 1000 characters",
            }),
            ingredient_status: Joi.string()
                .valid("active", "inactive")
                .default("active")
                .messages({
                    "any.only": "Status must be either active or inactive",
                }),
            waste_percentage: Joi.number()
                .min(0)
                .max(100)
                .precision(2)
                .allow(null)
                .messages({
                    "number.min": "Waste percentage cannot be negative",
                    "number.max": "Waste percentage cannot exceed 100%",
                }),
            unit_of_measure: Joi.number().positive().required().messages({
                "number.positive": "Unit of measure must be a positive number",
                "any.required": "Unit of measure is required",
            }),
            cost_per_unit: Joi.number().positive().precision(2).required().messages({
                "number.positive": "Cost per unit must be a positive number",
                "any.required": "Cost per unit is required",
            }),
            categories: Joi.array()
                .items(Joi.number().integer().positive())
                .min(1)
                .unique()
                .required()
                .messages({
                    "array.min": "At least one category is required",
                    "array.unique": "Duplicate categories are not allowed",
                    "any.required": "Categories are required",
                }),
            dietary_attributes: Joi.array()
                .items(Joi.number().integer().positive())
                .unique()
                .messages({
                    "array.unique": "Duplicate dietary attributes are not allowed",
                }),
            allergy_attributes: Joi.array()
                .items(Joi.number().integer().positive())
                .unique()
                .messages({
                    "array.unique": "Duplicate allergy attributes are not allowed",
                }),
            nutrition_attributes: Joi.array()
                .items(
                    Joi.object({
                        attribute_id: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .messages({
                                "number.positive": "Nutrition attribute ID must be positive",
                                "any.required": "Nutrition attribute ID is required",
                            }),
                        unit_of_measure: Joi.string()
                            .max(100)
                            .allow(null, "")
                            .messages({
                                "string.max": "Unit of measure cannot exceed 100 characters",
                            }),
                        unit: Joi.number().positive().allow(null).messages({
                            "number.positive": "Unit value must be positive",
                        }),
                    }),
                )
                .unique("attribute_id")
                .messages({
                    "array.unique": "Duplicate nutrition attributes are not allowed",
                }),
            conversions: Joi.array()
                .items(
                    Joi.object({
                        from_measure: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .messages({
                                "number.positive": "From measure must be positive",
                                "any.required": "From measure is required",
                            }),
                        from_measure_value: Joi.number()
                            .positive()
                            .precision(2)
                            .required()
                            .messages({
                                "number.positive": "From measure value must be positive",
                                "any.required": "From measure value is required",
                            }),
                        to_measure: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .invalid(Joi.ref("from_measure"))
                            .messages({
                                "number.positive": "To measure must be positive",
                                "any.required": "To measure is required",
                                "any.invalid": "The 'To' unit cannot be the same as the 'From' unit in conversions.",
                            }),
                        to_measure_value: Joi.number()
                            .positive()
                            .precision(2)
                            .required()
                            .messages({
                                "number.positive": "To measure value must be positive",
                                "any.required": "To measure value is required",
                            }),
                    }),
                )
                .custom((value, helpers) => {
                    // Check for duplicate conversions
                    const seen = new Set();
                    for (const conversion of value) {
                        const key = `${conversion.from_measure}-${conversion.to_measure}`;
                        if (seen.has(key)) {
                            return helpers.error("array.unique", {
                                message: `Duplicate conversion found: from measure ${conversion.from_measure} to measure ${conversion.to_measure}`,
                            });
                        }
                        seen.add(key);
                    }
                    return value;
                })
                .messages({
                    "array.unique": "Duplicate conversions are not allowed",
                }),
        }),
    });

const updateIngredientValidator = () =>
    celebrate({
        [Segments.BODY]: Joi.object().keys({
            ingredient_name: Joi.string().min(2).max(100).trim().messages({
                "string.min": "Ingredient name must be at least 2 characters long",
                "string.max": "Ingredient name cannot exceed 100 characters",
            }),
            ingredient_description: Joi.string().max(1000).allow(null, "").messages({
                "string.max": "Description cannot exceed 1000 characters",
            }),
            ingredient_status: Joi.string().valid("active", "inactive").messages({
                "any.only": "Status must be either active or inactive",
            }),
            waste_percentage: Joi.number()
                .min(0)
                .max(100)
                .precision(2)
                .allow(null)
                .messages({
                    "number.min": "Waste percentage cannot be negative",
                    "number.max": "Waste percentage cannot exceed 100%",
                }),
            unit_of_measure: Joi.number().integer().positive().messages({
                "number.positive": "Unit of measure must be a positive number",
            }),
            cost_per_unit: Joi.number().positive().precision(2).messages({
                "number.positive": "Cost per unit must be a positive number",
            }),
            categories: Joi.array()
                .items(Joi.number().integer().positive())
                .min(1)
                .unique()
                .messages({
                    "array.min": "At least one category is required",
                    "array.unique": "Duplicate categories are not allowed",
                }),
            dietary_attributes: Joi.array()
                .items(Joi.number().integer().positive())
                .unique()
                .messages({
                    "array.unique": "Duplicate dietary attributes are not allowed",
                }),
            allergy_attributes: Joi.array()
                .items(Joi.number().integer().positive())
                .unique()
                .messages({
                    "array.unique": "Duplicate allergy attributes are not allowed",
                }),
            nutrition_attributes: Joi.array()
                .items(
                    Joi.object({
                        attribute_id: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .messages({
                                "number.positive": "Nutrition attribute ID must be positive",
                                "any.required": "Nutrition attribute ID is required",
                            }),
                        unit_of_measure: Joi.string()
                            .max(100)
                            .allow(null, "")
                            .messages({
                                "string.max": "Unit of measure cannot exceed 100 characters",
                            }),
                        unit: Joi.number().positive().precision(3).allow(null).messages({
                            "number.positive": "Unit value must be positive",
                        }),
                    }),
                )
                .unique("attribute_id")
                .messages({
                    "array.unique": "Duplicate nutrition attributes are not allowed",
                }),
            conversions: Joi.array()
                .items(
                    Joi.object({
                        from_measure: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .messages({
                                "number.positive": "From measure must be positive",
                                "any.required": "From measure is required",
                            }),
                        from_measure_value: Joi.number()
                            .positive()
                            .precision(2)
                            .required()
                            .messages({
                                "number.positive": "From measure value must be positive",
                                "any.required": "From measure value is required",
                            }),
                        to_measure: Joi.number()
                            .integer()
                            .positive()
                            .required()
                            .invalid(Joi.ref("from_measure"))
                            .messages({
                                "number.positive": "To measure must be positive",
                                "any.required": "To measure is required",
                                "any.invalid": "The 'To' unit cannot be the same as the 'From' unit in conversions.",
                            }),
                        to_measure_value: Joi.number()
                            .positive()
                            .precision(2)
                            .required()
                            .messages({
                                "number.positive": "To measure value must be positive",
                                "any.required": "To measure value is required",
                            }),
                    }),
                )
                .custom((value, helpers) => {
                    // Check for duplicate conversions
                    const seen = new Set();
                    for (const conversion of value) {
                        const key = `${conversion.from_measure}-${conversion.to_measure}`;
                        if (seen.has(key)) {
                            return helpers.error("array.unique", {
                                message: `Duplicate conversion found: from measure ${conversion.from_measure} to measure ${conversion.to_measure}`,
                            });
                        }
                        seen.add(key);
                    }
                    return value;
                })
                .messages({
                    "array.unique": "Duplicate conversions are not allowed",
                }),
        }),
    });

const getIngredientValidator = () =>
    celebrate({
        [Segments.PARAMS]: Joi.object().keys({
            id: Joi.number().integer().positive().required().messages({
                "number.positive": "Ingredient ID must be a positive number",
                "any.required": "Ingredient ID is required",
            }),
        }),
    });

const importIngredientsValidator = () =>
    celebrate({
        [Segments.BODY]: Joi.object()
            .keys({
                // File validation will be handled by multer middleware
                // This validator can be used for additional body parameters if needed
            })
            .unknown(true), // Allow unknown fields for file upload
    });

const getIngredientsListValidator = () =>
    celebrate({
        [Segments.QUERY]: Joi.object().keys({
            page: Joi.number()
                .integer()
                .min(1)
                .default(1)
                .messages({
                    'number.min': 'Page must be at least 1'
                }),
            size: Joi.number()
                .integer()
                .min(1)
                .max(100)
                .default(10)
                .messages({
                    'number.min': 'Size must be at least 1',
                    'number.max': 'Size cannot exceed 100'
                }),
            search: Joi.string()
                .max(255)
                .allow('')
                .messages({
                    'string.max': 'Search term cannot exceed 255 characters'
                }),
            ingredient_status: Joi.string()
                .valid('active', 'inactive', 'all')
                .default('active')
                .messages({
                    'any.only': 'Status must be active, inactive, or all'
                }),
            category: Joi.alternatives()
                .try(
                    Joi.number().integer().positive(),
                    Joi.string().pattern(/^\d+(,\d+)*$/)
                )
                .messages({
                    'alternatives.match': 'Category must be a positive number or comma-separated numbers'
                }),
            sort_by: Joi.string()
                .valid('ingredient_name', 'cost_per_unit', 'waste_percentage', 'created_at', 'updated_at')
                .default('ingredient_name')
                .messages({
                    'any.only': 'Sort by must be one of: ingredient_name, cost_per_unit, waste_percentage, created_at, updated_at'
                }),
            sort_order: Joi.string()
                .valid('ASC', 'DESC', 'asc', 'desc')
                .default('ASC')
                .messages({
                    'any.only': 'Sort order must be ASC or DESC'
                }),
            download: Joi.string()
                .valid('excel', 'csv')
                .messages({
                    'any.only': 'Download format must be excel or csv'
                })
        })
    });

const deleteIngredientValidator = () =>
    celebrate({
        [Segments.PARAMS]: Joi.object().keys({
            id: Joi.number()
                .integer()
                .positive()
                .required()
                .messages({
                    'number.positive': 'Ingredient ID must be a positive number',
                    'any.required': 'Ingredient ID is required'
                })
        })
    });

// Default export object
export default {
    createIngredientValidator,
    updateIngredientValidator,
    getIngredientValidator,
    getIngredientsListValidator,
    deleteIngredientValidator,
    importIngredientsValidator,
};
