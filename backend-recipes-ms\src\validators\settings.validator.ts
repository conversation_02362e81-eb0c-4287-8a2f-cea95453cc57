import { celebrate, Joi, Segments } from "celebrate";

// Setting key validation patterns
const settingKeyPattern = /^[a-z_]+\.[a-z_]+$/;

// Common setting value types
const settingValueSchema = Joi.alternatives().try(
  Joi.string(),
  Joi.number(),
  Joi.boolean(),
  Joi.object(),
  Joi.array()
);

// Get settings by category validator
const getSettingsByCategoryValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object({
      category: Joi.string()
        .valid("recipe", "dashboard", "public", "analytics", "system")
        .optional(),
    }),
  });

// Get single setting by key validator
const getSettingByKeyValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object({
      key: Joi.string().pattern(settingKeyPattern).required().messages({
        "string.pattern.base":
          'Setting key must be in format "category.setting_name"',
      }),
    }),
  });

// Update multiple settings validator
const updateSettingsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .pattern(Joi.string().pattern(settingKeyPattern), settingValueSchema)
      .min(1)
      .required()
      .messages({
        "object.min": "At least one setting must be provided",
      }),
  });

// Update single setting validator
const updateSettingValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object({
      key: Joi.string().pattern(settingKeyPattern).required(),
    }),
    [Segments.BODY]: Joi.object({
      value: settingValueSchema.required(),
    }),
  });

// Reset settings validator
const resetSettingsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object({
      category: Joi.string()
        .valid("recipe", "dashboard", "public", "analytics", "system")
        .optional(),
    }),
  });

// Specific setting validations - matching UI exactly
export const specificSettingValidations = {
  // Recipe settings
  "recipe.public_store_enabled": Joi.boolean(),
  "recipe.highlight_changes": Joi.boolean(),

  // CTA Settings
  "recipe.cta_contact_form": Joi.boolean(),
  "recipe.cta_contact_info": Joi.boolean(),
  "recipe.cta_custom_link": Joi.boolean(),
  "recipe.cta_none": Joi.boolean(),

  // Contact Info Fields
  "recipe.contact_info_name": Joi.string().allow("").max(100),
  "recipe.contact_info_phone": Joi.string().allow("").max(20),
  "recipe.contact_info_email": Joi.string().allow("").email().max(255),
  "recipe.contact_info_link": Joi.string().allow("").uri().max(500),

  // Custom CTA Fields
  "recipe.custom_cta_text": Joi.string().allow("").max(100),
  "recipe.custom_cta_link": Joi.string().allow("").uri().max(500),

  // Display settings - exactly matching UI
  "recipe.display_category": Joi.boolean(),
  "recipe.display_ingredients": Joi.boolean(),
  "recipe.display_nutritional_information": Joi.boolean(),
  "recipe.display_allergen_information": Joi.boolean(),
  "recipe.display_preparation_steps": Joi.boolean(),
  "recipe.display_total_time": Joi.boolean(),
  "recipe.display_yield_portioning": Joi.boolean(),
  "recipe.display_cost": Joi.boolean(),
  "recipe.display_dietary_suitability": Joi.boolean(),
  "recipe.display_cuisine_type": Joi.boolean(),
  "recipe.display_media": Joi.boolean(),
  "recipe.display_links": Joi.boolean(),
  "recipe.display_scale": Joi.boolean(),
  "recipe.display_serve_in": Joi.boolean(),
  "recipe.display_garnish": Joi.boolean(),

  // Dashboard settings
  "dashboard.refresh_interval": Joi.number().min(30000).max(3600000), // 30 seconds to 1 hour
  "dashboard.default_date_range": Joi.string().valid(
    "last_7_days",
    "last_30_days",
    "last_90_days",
    "last_year"
  ),
  "dashboard.public_analytics_enabled": Joi.boolean(),

  // Analytics settings
  "analytics.track_anonymous_views": Joi.boolean(),
  "analytics.retention_days": Joi.number().min(1).max(2555), // 1 day to 7 years
  "analytics.enable_detailed_tracking": Joi.boolean(),

  // System settings (usually read-only for org admins)
  "system.max_file_size": Joi.number().min(1024).max(104857600), // 1KB to 100MB
  "system.allowed_file_types": Joi.array().items(Joi.string()),
  "system.backup_enabled": Joi.boolean(),
};

// Validate setting value based on key
export const validateSettingValue = (
  key: string,
  value: any
): { error?: any; value?: any } => {
  const validator =
    specificSettingValidations[key as keyof typeof specificSettingValidations];

  if (validator) {
    return validator.validate(value);
  }

  // Default validation for unknown settings
  return settingValueSchema.validate(value);
};

// Common validation messages
export const validationMessages = {
  INVALID_SETTING_KEY: 'Setting key must be in format "category.setting_name"',
  INVALID_SETTING_VALUE: "Setting value is invalid for the specified key",
  SETTING_NOT_FOUND: "Setting not found",
  CATEGORY_REQUIRED: "Category is required",
  VALUE_REQUIRED: "Value is required",
  SETTINGS_REQUIRED: "At least one setting must be provided",
};

// Recipe configuration settings validator
const publicRecipeCallToActionSchema = Joi.object({
  contactForm: Joi.boolean().optional(),
  contactInfo: Joi.object({
    enabled: Joi.boolean().optional(),
    name: Joi.string().allow("").max(100).optional(),
    phone: Joi.string().allow("").max(20).optional(),
    email: Joi.string().allow("").email().max(255).optional(),
    link: Joi.string().allow("").uri().max(500).optional(),
  }).optional(),
  customCtaLink: Joi.object({
    enabled: Joi.boolean().optional(),
    text: Joi.string().allow("").max(100).optional(),
    link: Joi.string().allow("").uri().max(500).optional(),
  }).optional(),
  none: Joi.boolean().optional(),
})
  .custom((value, helpers) => {
    // Determine which CTA options are enabled (truthy)
    const enabledFlags = [
      value?.contactForm,
      value?.contactInfo?.enabled,
      value?.customCtaLink?.enabled,
      value?.none,
    ].filter(Boolean);

    if (enabledFlags.length > 1) {
      return helpers.error("any.exclusiveCTA");
    }

    return value;
  })
  .messages({
    "any.exclusiveCTA":
      "Only one Call-To-Action option can be enabled at a time.",
  });

// Recipe configuration settings validator
const recipeConfigurationValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object({
      privateRecipeVisibilitySettings: Joi.object({
        highlightChanges: Joi.boolean().optional(),
      }).optional(),
      publicRecipeSettings: Joi.object({
        publicStoreAccess: Joi.boolean().optional(),
      }).optional(),
      publicRecipeCallToAction: publicRecipeCallToActionSchema.optional(),
      recipeDetailsToDisplayPublicly: Joi.object({
        category: Joi.boolean().optional(),
        ingredients: Joi.boolean().optional(),
        nutritionalInformation: Joi.boolean().optional(),
        allergenInformation: Joi.boolean().optional(),
        preparationSteps: Joi.boolean().optional(),
        totalTime: Joi.boolean().optional(),
        yieldPortioning: Joi.boolean().optional(),
        cost: Joi.boolean().optional(),
        dietarySuitability: Joi.boolean().optional(),
        cuisineType: Joi.boolean().optional(),
        media: Joi.boolean().optional(),
        links: Joi.boolean().optional(),
        scale: Joi.boolean().optional(),
        serveIn: Joi.boolean().optional(),
        garnish: Joi.boolean().optional(),
        haccp: Joi.boolean().optional(),
      }).optional(),

      // Allow updating organization slug
      organizationSlug: Joi.string().allow("").optional(),
    }),
  });

export default {
  getSettingsByCategoryValidator,
  getSettingByKeyValidator,
  updateSettingsValidator,
  updateSettingValidator,
  resetSettingsValidator,
  recipeConfigurationValidator,
};
