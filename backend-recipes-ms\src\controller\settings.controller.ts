import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import settingsService from "../services/settings.service";
import Settings, { SettingCategory } from "../models/Settings";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/transaction.helper";
import { Valida<PERSON>Helper } from "../helper/validation.helper";

/**
 * @description Get recipe settings configuration
 * @route GET /api/v1/private/settings/recipe-configuration
 * @access Private
 */
const getRecipeConfiguration = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Get all recipe and public settings
    const recipeSettings = await settingsService.getSettingsByCategory(
      SettingCategory.RECIPE,
      effectiveOrganizationId || undefined
    );
    const publicSettings = await settingsService.getSettingsByCategory(
      SettingCategory.PUBLIC,
      effectiveOrganizationId || undefined
    );

    const structuredSettings = {
      // Private Recipe Visibility Settings
      privateRecipeVisibilitySettings: {
        highlightChanges: recipeSettings["recipe.highlight_changes"] ?? false,
      },

      // Public Recipe Settings
      publicRecipeSettings: {
        publicStoreAccess:
          recipeSettings["recipe.public_store_enabled"] || false,
      },

      // Public Recipe Call-To-Action (CTA)
      publicRecipeCallToAction: {
        contactForm: publicSettings["recipe.cta_contact_form"] ?? true,
        contactInfo: {
          enabled: publicSettings["recipe.cta_contact_info"] || false,
          name: publicSettings["recipe.contact_info_name"] || "",
          phone: publicSettings["recipe.contact_info_phone"] || "",
          email: publicSettings["recipe.contact_info_email"] || "",
          link: publicSettings["recipe.contact_info_link"] || "",
        },
        customCtaLink: {
          enabled: publicSettings["recipe.cta_custom_link"] || false,
          text: publicSettings["recipe.custom_cta_text"] || "",
          link: publicSettings["recipe.custom_cta_link"] || "",
        },
        none: publicSettings["recipe.cta_none"] || false,
      },

      // Recipe Details to Display Publicly
      recipeDetailsToDisplayPublicly: {
        category: publicSettings["recipe.display_category"] ?? true,
        ingredients: publicSettings["recipe.display_ingredients"] ?? true,
        nutritionalInformation:
          publicSettings["recipe.display_nutritional_information"] ?? true,
        allergenInformation:
          publicSettings["recipe.display_allergen_information"] ?? true,
        preparationSteps:
          publicSettings["recipe.display_preparation_steps"] ?? true,
        totalTime: publicSettings["recipe.display_total_time"] ?? false,
        yieldPortioning:
          publicSettings["recipe.display_yield_portioning"] ?? false,
        cost: publicSettings["recipe.display_cost"] ?? false,
        dietarySuitability:
          publicSettings["recipe.display_dietary_suitability"] ?? false,
        cuisineType: publicSettings["recipe.display_cuisine_type"] ?? false,
        media: publicSettings["recipe.display_media"] ?? false,
        links: publicSettings["recipe.display_links"] ?? false,
        scale: publicSettings["recipe.display_scale"] ?? false,
        serveIn: publicSettings["recipe.display_serve_in"] ?? false,
        garnish: publicSettings["recipe.display_garnish"] ?? false,
        haccp: publicSettings["recipe.display_haccp"] ?? false,
      },

      // Organization unique slug (system setting)
      organizationSlug: recipeSettings["recipe.organization_name"] || "",
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_RETRIEVED"),
      data: structuredSettings,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching recipe configuration"
    );
  }
};

/**
 * @description Update recipe settings configuration
 * @route PUT /api/v1/private/settings/recipe-configuration
 * @access Private
 */
const updateRecipeConfiguration = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const uiSettings = sanitizedBody;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedBody.organization_id
      );

    const userId = req.user?.id;

    // Validate user ID
    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "User authentication required",
      });
    }

    // Convert UI structure back to flat settings
    const flatSettings: Record<string, any> = {};

    // Private Recipe Visibility Settings
    if (uiSettings.privateRecipeVisibilitySettings) {
      flatSettings["recipe.highlight_changes"] =
        uiSettings.privateRecipeVisibilitySettings.highlightChanges;
    }

    // Public Recipe Settings
    if (uiSettings.publicRecipeSettings) {
      flatSettings["recipe.public_store_enabled"] =
        uiSettings.publicRecipeSettings.publicStoreAccess;
    }

    // Public Recipe Call-To-Action
    if (uiSettings.publicRecipeCallToAction) {
      const cta = uiSettings.publicRecipeCallToAction;

      // Determine which option was explicitly enabled (true)
      const contactFormEnabled = cta.contactForm === true;
      const contactInfoEnabled = cta.contactInfo?.enabled === true;
      const customLinkEnabled = cta.customCtaLink?.enabled === true;
      const noneEnabled = cta.none === true;

      // Helper to decide final flag values (only one true)
      const only = (flag: boolean) => (flag ? true : false);

      // Contact Form
      if (cta.contactForm !== undefined || contactFormEnabled) {
        flatSettings["recipe.cta_contact_form"] = only(contactFormEnabled);
      }

      // Contact Info
      if (cta.contactInfo) {
        flatSettings["recipe.cta_contact_info"] = only(contactInfoEnabled);
        flatSettings["recipe.contact_info_name"] = cta.contactInfo.name || "";
        flatSettings["recipe.contact_info_phone"] = cta.contactInfo.phone || "";
        flatSettings["recipe.contact_info_email"] = cta.contactInfo.email || "";
        flatSettings["recipe.contact_info_link"] = cta.contactInfo.link || "";
      }

      // Custom CTA Link
      if (cta.customCtaLink) {
        flatSettings["recipe.cta_custom_link"] = only(customLinkEnabled);
        flatSettings["recipe.custom_cta_text"] = cta.customCtaLink.text || "";
        flatSettings["recipe.custom_cta_link"] = cta.customCtaLink.link || "";
      }

      // None
      if (cta.none !== undefined || noneEnabled) {
        flatSettings["recipe.cta_none"] = only(noneEnabled);
      }

      // Ensure exclusivity: if any one is true, others must become false (even if omitted)
      const anyEnabled =
        contactFormEnabled ||
        contactInfoEnabled ||
        customLinkEnabled ||
        noneEnabled;
      if (anyEnabled) {
        if (!contactFormEnabled)
          flatSettings["recipe.cta_contact_form"] = false;
        if (!contactInfoEnabled)
          flatSettings["recipe.cta_contact_info"] = false;
        if (!customLinkEnabled) flatSettings["recipe.cta_custom_link"] = false;
        if (!noneEnabled) flatSettings["recipe.cta_none"] = false;
      }
    }

    // Recipe Details to Display Publicly
    if (uiSettings.recipeDetailsToDisplayPublicly) {
      const details = uiSettings.recipeDetailsToDisplayPublicly;
      flatSettings["recipe.display_category"] = details.category;
      flatSettings["recipe.display_ingredients"] = details.ingredients;
      flatSettings["recipe.display_nutritional_information"] =
        details.nutritionalInformation;
      flatSettings["recipe.display_allergen_information"] =
        details.allergenInformation;
      flatSettings["recipe.display_preparation_steps"] =
        details.preparationSteps;
      flatSettings["recipe.display_total_time"] = details.totalTime;
      flatSettings["recipe.display_yield_portioning"] = details.yieldPortioning;
      flatSettings["recipe.display_cost"] = details.cost;
      flatSettings["recipe.display_dietary_suitability"] =
        details.dietarySuitability;
      flatSettings["recipe.display_cuisine_type"] = details.cuisineType;
      flatSettings["recipe.display_media"] = details.media;
      flatSettings["recipe.display_links"] = details.links;
      flatSettings["recipe.display_scale"] = details.scale;
      flatSettings["recipe.display_serve_in"] = details.serveIn;
      flatSettings["recipe.display_garnish"] = details.garnish;
      flatSettings["recipe.display_haccp"] = details.haccp;
    }

    // Organization slug (system setting)
    if (uiSettings.organizationSlug !== undefined) {
      flatSettings["recipe.organization_name"] = String(
        uiSettings.organizationSlug
      ).trim();
    }

    // Update all settings
    await settingsService.updateSettings(
      flatSettings,
      effectiveOrganizationId || undefined,
      userId
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error updating recipe configuration"
    );
  }
};

/**
 * @description Get list of all organization slugs (recipe.organization_name)
 * @route GET /api/v1/private/settings/organization-slugs
 * @access Private (super-admin)
 */
const listOrganizationSlugs = async (_req: any, res: any): Promise<any> => {
  try {
    // Sanitize input to avoid injection / malformed params
    const sanitizedQuery = ValidationHelper.sanitizeInput(_req.query);
    const { slug, organization_id } = sanitizedQuery;

    if (!slug) {
      return res.status(400).json({ message: "Slug  is required." });
    }

    // Get effective organization ID (handles admin users) so that we can
    // ignore the slug already assigned to the same organisation that is
    // performing the check.
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        _req.user,
        organization_id
      );

    // Look for an existing slug record
    const existingSlug = await Settings.findOne({
      where: {
        setting_key: "recipe.organization_name",
        setting_value: slug,
      },
      // We need the organisation id to determine ownership of the slug
      attributes: ["id", "organization_id"],
      raw: true,
    });

    // If nothing found, slug is available for sure
    if (!existingSlug) {
      return res.json({ available: true, message: "Slug available." });
    }

    // If the found slug belongs to the same organisation that is making the
    // request, we should treat it as available (unchanged slug).
    if (
      effectiveOrganizationId &&
      existingSlug.organization_id === String(effectiveOrganizationId)
    ) {
      return res.json({
        available: true,
        message: "Slug unchanged (current organisation).",
      });
    }

    // Slug belongs to a different organisation → taken
    return res.json({ available: false, message: "Slug already taken." });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching organization slugs"
    );
  }
};

// ============================================================================
// EXPORT ONLY ESSENTIAL FUNCTIONS - Just 2 APIs needed!
// ============================================================================

export default {
  getRecipeConfiguration,
  updateRecipeConfiguration,
  listOrganizationSlugs,
};
