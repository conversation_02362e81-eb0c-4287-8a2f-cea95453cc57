import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import * as ExcelJS from "exceljs";
import analyticsService from "../services/analytics.service";
import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import Recipe from "../models/Recipe";
import { ValidationHelper } from "../helper/validation.helper";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "../helper/transaction.helper";
import { getOrgName } from "../helper/common";

/**
 * Track CTA clicks on public recipes
 * @route POST /api/v1/public/analytics/track/cta-click
 */
const trackCtaClick = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name, cta_type, cta_text } = sanitizedBody;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
        },
      });
    }

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CTA_CLICK,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        cta_type,
        cta_text,
        tracking_source: req.user ? "authenticated" : "public",
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "CTA click tracked successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error tracking CTA click"
    );
  }
};

/**
 * Submit contact form from public recipes
 * @route POST /api/v1/public/analytics/contact-form
 */
const submitContactForm = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name, name, email, mobile, message } =
      sanitizedBody;

    // Validate required fields
    if (!recipe_id || !name || !email) {
      return res.status(400).json({
        status: false,
        message: "recipe_id, name, and email are required",
        missing_fields: {
          recipe_id: !recipe_id,
          name: !name,
          email: !email,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate recipe_id data type
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        status: false,
        message: "Invalid email format",
      });
    }

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CONTACT_FORM_SUBMIT,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        contact_name: name,
        contact_email: email,
        contact_mobile: mobile,
        message,
        tracking_source: req.user ? "authenticated" : "public",
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Contact form submitted successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error submitting contact form"
    );
  }
};

/**
 * Track recipe views on public recipes
 * @route POST /v1/public/analytics/track/recipe-view
 */
const trackRecipeView = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name } = sanitizedBody;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
        },
      });
    }

    // Track recipe view only
    const metadata: any = {
      recipe_name,
      timestamp: new Date().toISOString(),
      tracking_source: req.user ? "authenticated" : "public",
    };

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.RECIPE_VIEW,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: metadata,
    });

    // Increment recipe_impression column as well (keeps parity with private API)
    await Recipe.increment("recipe_impression", {
      where: { id: recipeIdNumber },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe view tracked successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error tracking recipe activity"
    );
  }
};

/**
 * Get CTA click analytics with pagination
 * @route GET /api/v1/private/analytics/cta-clicks
 */
const getCtaClickAnalytics = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      time_period, // Support both date_range and time_period
      start_date,
      end_date,
      sort_order = "desc",
      sort_by,
      page,
      limit,
      cta_type,
      recipe_name,
      search,
    } = sanitizedQuery;

    // Use time_period as fallback for date_range, with default
    const finalDateRange = date_range || time_period || "last_30_days";

    // Validate date_range parameter if provided
    if (finalDateRange) {
      const validDateRanges = [
        "today",
        "this_week",
        "this_month",
        "last_7_days",
        "last_month",
        "last_30_days",
        "last_90_days",
        "last_year",
        "this_year",
        "all_time",
        "custom",
      ];
      if (!validDateRanges.includes(finalDateRange)) {
        return res.status(400).json({
          status: false,
          message:
            "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0",
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    const result = await analyticsService.getCtaClickAnalytics(
      effectiveOrganizationId || undefined,
      finalDateRange as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber,
      cta_type,
      recipe_name || search, // Use search as fallback for recipe_name
      sort_order,
      sort_by
    );

    // Sort results if no pagination (pagination already handles sorting)
    if (!pageNumber && !limitNumber) {
      if (sort_order === "asc") {
        result.data.sort((a: any, b: any) => a.clicks - b.clicks);
      } else {
        result.data.sort((a: any, b: any) => b.clicks - a.clicks);
      }
    }

    const response: any = {
      status: true,
      message: res.__("CTA_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: result.data,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: result.total,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching CTA analytics"
    );
  }
};

/**
 * Get contact form submission analytics with pagination
 * @route GET /api/v1/private/analytics/contact-analytics
 */
const getContactSubmissionAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      start_date,
      end_date,
      recipe_id,
      page,
      limit,
      search,
      sort_order,
      sort_by,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate date_range parameter if provided - match validator exactly
    if (date_range) {
      const validDateRanges = [
        "today",
        "this_week",
        "this_month",
        "last_7_days",
        "last_month",
        "last_30_days",
        "last_90_days",
        "last_year",
        "custom",
      ];
      if (!validDateRanges.includes(date_range)) {
        return res.status(400).json({
          status: false,
          message:
            "date_range must be one of [" + validDateRanges.join(", ") + "]",
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0",
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100",
      });
    }

    const result = await analyticsService.getContactSubmissionAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber,
      search, // Unified search parameter only
      sort_order,
      sort_by
    );

    // Filter by recipe if specified (only when no pagination to avoid incorrect counts)
    let filteredData = result.data;
    let filteredTotal = result.total;

    if (recipe_id && !pageNumber && !limitNumber) {
      filteredData = result.data.filter(
        (item: any) => item.recipe_id === Number(recipe_id)
      );
      filteredTotal = filteredData.length;
    }

    const response: any = {
      status: true,
      message: res.__("CONTACT_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: filteredData,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        recipe_filter: recipe_id || null,
        total_records: filteredTotal,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching contact analytics"
    );
  }
};

/**
 * Get recipe view analytics
 * @route GET /api/v1/private/analytics/recipe-views
 */
const getRecipeViewAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range = "last_30_days",
      start_date,
      end_date,
      sort_order = "desc",
      sort_by = "view_count",
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    const analytics = await analyticsService.getRecipeViewAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date
    );

    // Sort results by specified field and order
    if (sort_by === "view_count") {
      if (sort_order === "asc") {
        analytics.sort((a: any, b: any) => a.total_views - b.total_views);
      } else {
        analytics.sort((a: any, b: any) => b.total_views - a.total_views);
      }
    } else if (sort_by === "recipe_name") {
      if (sort_order === "asc") {
        analytics.sort((a: any, b: any) =>
          a.recipe_name.localeCompare(b.recipe_name)
        );
      } else {
        analytics.sort((a: any, b: any) =>
          b.recipe_name.localeCompare(a.recipe_name)
        );
      }
    } else if (sort_by === "last_viewed") {
      if (sort_order === "asc") {
        analytics.sort(
          (a: any, b: any) =>
            new Date(a.last_viewed).getTime() -
            new Date(b.last_viewed).getTime()
        );
      } else {
        analytics.sort(
          (a: any, b: any) =>
            new Date(b.last_viewed).getTime() -
            new Date(a.last_viewed).getTime()
        );
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_VIEW_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: analytics,
      meta: {
        date_range,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: analytics.length,
        sort_order: sort_order,
        sort_by: sort_by,
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching recipe view analytics"
    );
  }
};

/**
 * Export contact form submissions to CSV or Excel
 * @route GET /api/v1/private/analytics/contact-submissions/export
 */
const exportContactSubmissions = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      format = "excel",
      date_range = "last_30_days",
      start_date,
      end_date,
      recipe_name,
      user_email,
      search,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate format
    if (!["csv", "excel"].includes(format)) {
      return res.status(400).json({
        status: false,
        message: "Invalid format. Supported formats: csv, excel",
      });
    }

    // Get all contact submissions without pagination for export
    const result = await analyticsService.getContactSubmissionAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      undefined, // no pagination
      undefined, // no limit
      recipe_name || search,
      user_email || search
    );

    if (!result.data || result.data.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: "No contact submissions found for export.",
        data: { count: 0 },
      });
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");

    if (format === "csv") {
      // Convert to CSV format
      const csv = convertContactSubmissionsToCSV(result.data, date_range);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=contact-analytics-export-${timestamp}.csv`
      );
      return res.send(csv);
    } else if (format === "excel") {
      // Convert to Excel format
      const buffer = await convertContactSubmissionsToExcel(
        result.data,
        date_range
      );
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=contact-analytics-export-${timestamp}.xlsx`
      );
      return res.send(buffer);
    }

    // Default Excel export
    return await exportContactSubmissionsToExcel(
      result.data,
      date_range,
      effectiveOrganizationId,
      res
    );
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting contact submissions"
    );
  }
};

/**
 * Export CTA click analytics to CSV or Excel
 * @route GET /api/v1/private/analytics/cta-analytics/export
 */
const exportCtaAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      format = "excel",
      date_range = "last_30_days",
      start_date,
      end_date,
      recipe_name,
      cta_type,
      search,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate format
    if (!["csv", "excel"].includes(format)) {
      return res.status(400).json({
        status: false,
        message: "Invalid format. Supported formats: csv, excel",
      });
    }

    // Get raw CTA analytics data for export (not aggregated)
    const result = await analyticsService.getCtaClickAnalyticsRaw(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      cta_type,
      recipe_name || search
    );

    if (!result.data || result.data.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: "No CTA analytics found for export.",
        data: { count: 0 },
      });
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");

    if (format === "csv") {
      // Convert to CSV format
      const csv = convertCtaAnalyticsToCSV(result.data, date_range);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=cta-analytics-export-${timestamp}.csv`
      );
      return res.send(csv);
    } else if (format === "excel") {
      // Convert to Excel format
      const buffer = await convertCtaAnalyticsToExcel(
        result.data,
        date_range
      );
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=cta-analytics-export-${timestamp}.xlsx`
      );
      return res.send(buffer);
    }

    // Default Excel export
    return await exportCtaAnalyticsToExcel(
      result.data,
      date_range,
      effectiveOrganizationId,
      res
    );
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting CTA analytics"
    );
  }
};

/**
 * Delete contact form submission
 * @route DELETE /api/v1/private/analytics/contact-analytics/:id
 */
const deleteContactSubmission = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;

    // Validate ID parameter
    if (!id || isNaN(Number(id))) {
      return res.status(400).json({
        status: false,
        message: "Valid submission ID is required",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(req.user);

    // Build where clause
    const whereClause: any = {
      id: Number(id),
      event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
    };

    // Add organization filter if not admin
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    const deleted = await Analytics.destroy({
      where: whereClause,
    });

    if (!deleted) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Contact submission not found",
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONTACT_SUBMISSION_DELETED_SUCCESSFULLY"),
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting contact submission"
    );
  }
};

/**
 * Bulk delete contact form submissions from analytics
 * @route DELETE /api/v1/private/analytics/contact-analytics/bulk-delete
 */
const bulkDeleteContactSubmissions = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const { submission_ids, delete_all = false, filters = {} } = sanitizedBody;

    // Validate input
    if (
      !delete_all &&
      (!submission_ids ||
        !Array.isArray(submission_ids) ||
        submission_ids.length === 0)
    ) {
      return res.status(400).json({
        status: false,
        message:
          "Please provide submission_ids array or set delete_all to true with filters.",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(req.user);

    let deletedCount = 0;
    const whereClause: any = {
      event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
    };

    // Add organization filter if not admin
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    if (delete_all) {
      // Build where clause from filters
      const { search, recipe_name, user_email, start_date, end_date } = filters;

      // Apply date range filter
      if (start_date || end_date) {
        const dateFilter: any = {};
        if (start_date) {
          dateFilter[Op.gte] = new Date(start_date as string);
        }
        if (end_date) {
          const endDateTime = new Date(end_date as string);
          endDateTime.setHours(23, 59, 59, 999); // End of day
          dateFilter[Op.lte] = endDateTime;
        }
        whereClause.created_at = dateFilter;
      }

      // Apply search filters using metadata JSON queries
      if (search && typeof search === "string" && search.trim()) {
        const searchTerm = search.trim();
        whereClause[Op.or] = [
          { "$metadata.contact_name$": { [Op.iLike]: `%${searchTerm}%` } },
          { "$metadata.contact_email$": { [Op.iLike]: `%${searchTerm}%` } },
          { "$metadata.recipe_name$": { [Op.iLike]: `%${searchTerm}%` } },
        ];
      }

      if (
        recipe_name &&
        typeof recipe_name === "string" &&
        recipe_name.trim()
      ) {
        whereClause["$metadata.recipe_name$"] = {
          [Op.iLike]: `%${recipe_name.trim()}%`,
        };
      }

      if (user_email && typeof user_email === "string" && user_email.trim()) {
        whereClause["$metadata.contact_email$"] = {
          [Op.iLike]: `%${user_email.trim()}%`,
        };
      }

      // Delete all matching records
      deletedCount = await Analytics.destroy({
        where: whereClause,
      });
    } else {
      // Validate submission IDs
      const validIds = submission_ids.filter((id: any) => {
        const numId = parseInt(id, 10);
        return !isNaN(numId) && numId > 0;
      });

      if (validIds.length === 0) {
        return res.status(400).json({
          status: false,
          message: "No valid submission IDs provided.",
        });
      }

      // Delete specific submissions
      whereClause.id = { [Op.in]: validIds };
      deletedCount = await Analytics.destroy({
        where: whereClause,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Successfully deleted ${deletedCount} contact form submission(s) from analytics.`,
      data: {
        deleted_count: deletedCount,
        operation: delete_all ? "bulk_delete_filtered" : "bulk_delete_by_ids",
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error bulk deleting contact form submissions"
    );
  }
};

/**
 * Get analytics summary - Real data from database
 * @route GET /api/v1/private/analytics
 */
const getAnalyticsSummary = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      page,
      limit,
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Get real analytics data from service
    const summary = await analyticsService.getAnalyticsSummary({
      organizationId: effectiveOrganizationId || undefined,
      page: parseInt(page) || 1,
      limit: Math.min(parseInt(limit) || 10, 50),
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Analytics summary fetched successfully",
      data: summary,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching analytics summary"
    );
  }
};

/**
 * Get recipe view statistics for private recipes with assigned users
 * @route GET /api/v1/private/analytics/recipe-view-statistics/:recipeId
 */
const getRecipeViewStatistics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedParams = ValidationHelper.sanitizeInput(req.params);
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipeId } = sanitizedParams;
    // Accept user_ids from either query string or request body
    const user_ids =
      sanitizedQuery.user_ids !== undefined
        ? sanitizedQuery.user_ids
        : sanitizedBody?.user_ids;

    // Convert recipeId to number
    const recipeIdNumber = parseInt(recipeId);
    if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "Invalid recipe ID. Must be a positive number",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        req.query.organization_id
      );

    // Call service method
    const result = await analyticsService.getRecipeViewStatistics(
      recipeIdNumber,
      effectiveOrganizationId || undefined
    );
    // Return response
    if (!result.status) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: result.message,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching recipe view statistics"
    );
  }
};

/**
 * Get user-friendly activity feed
 * @route GET /api/v1/private/analytics/activity-feed
 */
const getActivityFeed = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      limit = "10",
      format = "formal", // formal, casual, short
    } = sanitizedQuery;

    // Validate limit
    const limitNumber = parseInt(limit);
    if (isNaN(limitNumber) || limitNumber <= 0 || limitNumber > 100) {
      return res.status(400).json({
        status: false,
        message: "Limit must be a number between 1 and 100",
      });
    }

    // Validate format
    const validFormats = ["formal", "casual", "short"];
    if (!validFormats.includes(format)) {
      return res.status(400).json({
        status: false,
        message: `Format must be one of: ${validFormats.join(", ")}`,
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Get current user ID for personalized messages
    const currentUserId = req.user?.id;

    // Get activity with context
    const activities = await analyticsService.getRecentActivityWithContext(
      effectiveOrganizationId || undefined,
      currentUserId,
      limitNumber
    );

    // Format response based on requested format
    const formattedActivities = activities.map((activity: any) => {
      let message: string;
      switch (format) {
        case "casual":
          message = activity.activity_variations.casual;
          break;
        case "short":
          message = activity.activity_variations.short;
          break;
        default:
          message = activity.activity_description; // formal
      }

      return {
        id: `${activity.event_type}_${activity.recipe_id}_${activity.created_at}`,
        message: message,
        recipe_id: activity.recipe_id,
        recipe_name: activity.recipe_name,
        event_type: activity.event_type,
        created_at: activity.created_at,
        user_info: {
          user_id: activity.user_id,
          user_name: activity.user_name,
          user_email: activity.user_email,
          is_current_user: activity.is_current_user,
          is_anonymous: activity.is_anonymous,
        },
        // Include all variations for frontend flexibility
        message_variations: {
          formal: activity.activity_description,
          casual: activity.activity_variations.casual,
          short: activity.activity_variations.short,
        },
      };
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("ACTIVITY_FEED_FETCHED_SUCCESSFULLY") ||
        "Activity feed retrieved successfully",
      data: formattedActivities,
      meta: {
        total_records: formattedActivities.length,
        limit: limitNumber,
        format: format,
        organization_id: effectiveOrganizationId,
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching activity feed"
    );
  }
};

/**
 * Reset recipe view statistics for private recipes with assigned users
 * @route DELETE /api/v1/private/analytics/reset-view-statistics/:recipeId?user_ids=55,56,57
 */
const resetRecipeViewStatistics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedParams = ValidationHelper.sanitizeInput(req.params);
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const { recipeId } = sanitizedParams;
    const { user_ids } = sanitizedQuery;

    // Convert recipeId to number
    const recipeIdNumber = parseInt(recipeId);
    if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "Invalid recipe ID. Must be a positive number",
      });
    }

    // Validate user_ids if provided (can be array of user IDs or the string "all")
    let userIds: number[] | string | undefined;

    if (user_ids !== undefined) {
      if (user_ids === "all") {
        // Handle "all" case - reset all user statistics
        userIds = "all";
      } else if (typeof user_ids === "string" && user_ids.trim() !== "") {
        // Handle comma-separated user IDs
        try {
          const userIdStrings = user_ids.split(",").map((id) => id.trim());
          userIds = userIdStrings.map((id: string) => {
            const userId = parseInt(id);
            if (isNaN(userId) || userId <= 0) {
              throw new Error(
                `Invalid user ID: ${id}. Must be a positive number`
              );
            }
            return userId;
          });

          if (userIds.length === 0) {
            return res.status(400).json({
              status: false,
              message: "user_ids cannot be empty when provided",
            });
          }
        } catch (error: any) {
          return res.status(400).json({
            status: false,
            message: error.message,
          });
        }
      } else {
        return res.status(400).json({
          status: false,
          message:
            "user_ids must be either comma-separated user IDs (e.g., '55,56,57') or the string 'all'",
        });
      }
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Call service method
    const result = await analyticsService.resetRecipeViewStatistics(
      recipeIdNumber,
      effectiveOrganizationId || undefined,
      userIds
    );
    if (!result.status) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: result.message,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error resetting recipe view statistics"
    );
  }
};

// Helper function to convert contact submissions to CSV
function convertContactSubmissionsToCSV(
  data: any[],
  dateRange: string = "last_30_days"
): string {
  const headers = [
    "Recipe ID",
    "Recipe Name",
    "Contact Name",
    "Email",
    "Mobile",
    "Message",
    "Submitted At",
    "Days Ago",
  ];

  const rows = data.map((item) => {
    // Calculate days ago
    const daysAgo = item.submitted_on
      ? Math.floor(
        (new Date().getTime() - new Date(item.submitted_on).getTime()) /
        (1000 * 60 * 60 * 24)
      )
      : "N/A";

    return [
      item.recipe_id || "N/A",
      item.recipe_name || "Unknown Recipe",
      item.name || "N/A", // Fixed: use 'name' instead of 'contact_name'
      item.email || "N/A", // Fixed: use 'email' instead of 'contact_email'
      item.mobile || "N/A", // Fixed: use 'mobile' instead of 'contact_mobile'
      (item.message || "").replace(/"/g, '""').replace(/\n/g, " "), // Escape quotes and newlines
      item.submitted_on ? new Date(item.submitted_on).toLocaleString() : "N/A", // Fixed: use 'submitted_on'
      daysAgo,
    ];
  });

  const csvContent = [
    `# Contact Form Submissions Export - ${new Date().toISOString()}`,
    `# Date Range: ${dateRange}`,
    `# Generated by Recipe Management System`,
    "",
    headers.join(","),
    ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  return csvContent;
}

// Helper function to export contact submissions to Excel
async function exportContactSubmissionsToExcel(
  data: any[],
  dateRange: string,
  organizationId: string | null | undefined,
  res: Response
): Promise<any> {
  try {
    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Contact Submissions Export");

    // Get organization name
    const orgName = organizationId
      ? await getOrgName(organizationId)
      : "Organization";

    // Add organization name at the top with styling
    const orgRow = worksheet.addRow([orgName || "Organization"]);
    orgRow.font = { bold: true, size: 16, color: { argb: "FF135e96" } };
    orgRow.alignment = { horizontal: "center" };
    worksheet.mergeCells(1, 1, 1, 5); // Merge across 5 columns only

    // Add empty row for spacing
    worksheet.addRow([]);

    // Add export information with better formatting
    const dateRow = worksheet.addRow([
      `Export Date: ${new Date().toLocaleString()}`,
    ]);
    dateRow.font = { bold: true, size: 12 };

    const rangeRow = worksheet.addRow([`Date Range: ${dateRange}`]);
    rangeRow.font = { bold: true, size: 12 };

    const countRow = worksheet.addRow([`Total Records: ${data.length}`]);
    countRow.font = { bold: true, size: 12 };

    worksheet.addRow([]); // Empty row

    // Add headers with improved styling
    const headers = [
      "Recipe ID",
      "Recipe Name",
      "Contact Name",
      "Email",
      "Mobile",
      "Message",
      "Submitted At",
      "Days Ago",
    ];

    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF135e96" },
    };
    headerRow.alignment = { horizontal: "center", vertical: "middle" };
    headerRow.height = 25;

    // Add data rows
    data.forEach((item, index) => {
      const dataRow = worksheet.addRow([
        item.recipe_id || "N/A",
        item.recipe_name || "Unknown Recipe",
        item.contact_name || "N/A",
        item.contact_email || "N/A",
        item.contact_mobile || "N/A",
        item.message || "N/A",
        item.submitted_at || "N/A",
        item.time_ago || "N/A",
      ]);

      // Add alternating row colors for better readability
      if (index % 2 === 0) {
        dataRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF8F9FA" }, // Light gray for even rows
        };
      }
    });

    // Auto-fit columns
    worksheet.columns.forEach((column, index) => {
      let maxLength = 0;
      const columnHeader = headers[index];

      // Check header length
      if (columnHeader) {
        maxLength = Math.max(maxLength, columnHeader.length);
      }

      // Check data length in each cell of this column
      if (column && column.eachCell) {
        column.eachCell({ includeEmpty: false }, (cell) => {
          const cellValue = cell.value ? cell.value.toString() : "";
          maxLength = Math.max(maxLength, cellValue.length);
        });
      }

      // Set width with minimum of 12 and maximum of 50 characters
      const calculatedWidth = Math.min(Math.max(maxLength + 3, 12), 50);
      if (column) {
        column.width = calculatedWidth;
      }
    });

    // Add freeze panes to keep headers visible
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 6, // Freeze at row 6 (header row)
        topLeftCell: "A7",
        activeCell: "A7",
      },
    ];

    // Add auto-filter to header row
    worksheet.autoFilter = {
      from: "A6",
      to: `H${6 + data.length}`, // Adjust based on number of columns and rows
    };

    // Add print settings
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: "landscape",
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3,
      },
    };

    // Add header and footer for printing
    worksheet.headerFooter.oddHeader = `&C&"Arial,Bold"&14${orgName || "Organization"} - Contact Submissions Export`;
    worksheet.headerFooter.oddFooter = `&L&"Arial"&10Generated on: ${new Date().toLocaleString()}&R&"Arial"&10Page &P of &N`;

    // Set response headers
    const filename = `contact-submissions-export-${new Date().toISOString().split("T")[0]}.xlsx`;
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Excel export error:", error);
    throw error;
  }
}

// Helper function to convert contact submissions to Excel
async function convertContactSubmissionsToExcel(
  data: any[],
  dateRange: string = "last_30_days"
): Promise<Buffer> {
  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Contact Submissions");

  // Add export information
  const titleRow = worksheet.addRow([
    `Contact Form Submissions Export - ${new Date().toISOString()}`,
  ]);
  titleRow.font = { bold: true, size: 14, color: { argb: "FF135e96" } };
  titleRow.alignment = { horizontal: "center" };
  worksheet.mergeCells(1, 1, 1, 5); // Merge across 5 columns only

  const dateRangeRow = worksheet.addRow([`Date Range: ${dateRange}`]);
  dateRangeRow.font = { bold: true, size: 12 };

  const totalRow = worksheet.addRow([`Total Records: ${data.length}`]);
  totalRow.font = { bold: true, size: 12 };

  worksheet.addRow([]); // Empty row

  // Add headers with styling
  const headers = [
    "S.No",
    "Recipe ID",
    "Recipe Name",
    "Contact Name",
    "Email",
    "Mobile",
    "Message",
    "Submitted At",
    "Days Ago",
  ];

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FF135e96" },
  };
  headerRow.alignment = { horizontal: "center", vertical: "middle" };
  headerRow.height = 25;

  // Add data rows
  data.forEach((item: any, index: number) => {
    // Calculate days ago
    const daysAgo = item.submitted_on
      ? Math.floor(
        (new Date().getTime() - new Date(item.submitted_on).getTime()) /
        (1000 * 60 * 60 * 24)
      )
      : "N/A";

    const dataRow = worksheet.addRow([
      index + 1,
      item.recipe_id || "N/A",
      item.recipe_name || "Unknown Recipe",
      item.name || "N/A",
      item.email || "N/A",
      item.mobile || "N/A",
      item.message || "N/A",
      item.submitted_on ? new Date(item.submitted_on).toLocaleString() : "N/A",
      daysAgo,
    ]);

    // Add alternating row colors for better readability
    if (index % 2 === 0) {
      dataRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF8F9FA" },
      };
    }
  });

  // Set column widths
  worksheet.columns = [
    { width: 8 }, // S.No
    { width: 12 }, // Recipe ID
    { width: 25 }, // Recipe Name
    { width: 20 }, // Contact Name
    { width: 30 }, // Email
    { width: 15 }, // Mobile
    { width: 50 }, // Message
    { width: 20 }, // Submitted At
    { width: 12 }, // Days Ago
  ];

  // Add borders to all cells
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });
  });

  // Add freeze panes to keep headers visible
  worksheet.views = [
    {
      state: "frozen",
      xSplit: 0,
      ySplit: 5, // Freeze at row 5 (header row)
      topLeftCell: "A6",
      activeCell: "A6",
    },
  ];

  // Generate and return buffer
  const buffer = (await workbook.xlsx.writeBuffer()) as Buffer;
  return buffer;
}

// Helper function to aggregate CTA data for export (matching frontend table structure)
function aggregateCtaDataForExport(rawData: any[]): any[] {
  const aggregated = new Map();
  let idCounter = 1;

  rawData.forEach((item: any) => {
    const recipeName = item.recipe_name_meta || item.recipe_title || "Unknown Recipe";
    const ctaType = item.cta_type || "Contact Info";
    const key = `${item.recipe_id}-${ctaType}`;

    if (aggregated.has(key)) {
      const existing = aggregated.get(key);
      existing.clicks += 1;
      // Update last clicked if this one is more recent
      if (new Date(item.created_at) > new Date(existing.last_clicked_at)) {
        existing.last_clicked_at = item.created_at;
      }
    } else {
      aggregated.set(key, {
        id: idCounter++,
        recipe_name: recipeName,
        recipe_id: item.recipe_id,
        cta_type: ctaType,
        clicks: 1,
        last_clicked_at: item.created_at,
      });
    }
  });

  return Array.from(aggregated.values()).sort(
    (a, b) => new Date(b.last_clicked_at).getTime() - new Date(a.last_clicked_at).getTime()
  );
}

// Helper function to convert CTA analytics to CSV
function convertCtaAnalyticsToCSV(
  data: any[],
  dateRange: string = "last_30_days"
): string {
  const headers = [
    "ID",
    "Recipe Name",
    "CTA Type",
    "Clicks",
    "Last Clicked At",
  ];

  // Aggregate data to match frontend table structure
  const aggregatedData = aggregateCtaDataForExport(data);

  const rows = aggregatedData.map((item: any) => {
    return [
      item.id || "N/A",
      item.recipe_name || "Unknown Recipe",
      item.cta_type || "Contact Info",
      item.clicks || 0,
      item.last_clicked_at ? new Date(item.last_clicked_at).toLocaleString() : "N/A",
    ];
  });

  const csvContent = [
    `# CTA Click Analytics Export - ${new Date().toISOString()}`,
    `# Date Range: ${dateRange}`,
    `# Generated by Recipe Management System`,
    "",
    headers.join(","),
    ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  return csvContent;
}

// Helper function to convert CTA analytics to Excel
async function convertCtaAnalyticsToExcel(
  data: any[],
  dateRange: string = "last_30_days"
): Promise<Buffer> {
  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("CTA Analytics");

  // Add export information
  const titleRow = worksheet.addRow([
    `CTA Click Analytics Export - ${new Date().toISOString()}`,
  ]);
  titleRow.font = { bold: true, size: 14 };
  titleRow.alignment = { horizontal: "center" };
  worksheet.mergeCells("A1:E1");

  const dateRangeRow = worksheet.addRow([`Date Range: ${dateRange}`]);
  dateRangeRow.font = { bold: true, size: 12 };
  worksheet.mergeCells("A2:E2");

  const totalRow = worksheet.addRow([`Total Records: ${data.length}`]);
  totalRow.font = { bold: true, size: 12 };
  worksheet.mergeCells("A3:E3");

  worksheet.addRow([]); // Empty row

  // Add headers with styling (matching frontend table)
  const headers = [
    "ID",
    "Recipe Name",
    "CTA Type",
    "Clicks",
    "Last Clicked At",
  ];

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FF135e96" },
  };
  headerRow.alignment = { horizontal: "center", vertical: "middle" };
  headerRow.height = 25;

  // Apply header styling to each cell
  headerRow.eachCell((cell) => {
    cell.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF135e96" },
    };
    cell.alignment = { horizontal: "center", vertical: "middle" };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });

  // Aggregate data to match frontend table structure
  const aggregatedData = aggregateCtaDataForExport(data);

  // Add data rows
  aggregatedData.forEach((item: any, index: number) => {
    const dataRow = worksheet.addRow([
      item.id,
      item.recipe_name || "Unknown Recipe",
      item.cta_type || "Contact Info",
      item.clicks || 0,
      item.last_clicked_at ? new Date(item.last_clicked_at).toLocaleString() : "N/A",
    ]);

    // Add alternating row colors for better readability
    if (index % 2 === 0) {
      dataRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF8F9FA" },
      };
    }
  });

  // Set column widths for exactly 5 columns only
  worksheet.columns = [
    { width: 8 }, // ID
    { width: 30 }, // Recipe Name
    { width: 15 }, // CTA Type
    { width: 12 }, // Clicks
    { width: 20 }, // Last Clicked At
  ];

  // Calculate correct total rows: 3 title rows + 1 empty + 1 header + data rows
  const totalDataRows = 5 + aggregatedData.length; // Row 1-3: titles, Row 4: empty, Row 5: header, Row 6+: data

  // Add borders to all cells with data (5 columns: A to E only)
  for (let rowIndex = 1; rowIndex <= totalDataRows; rowIndex++) {
    for (let colIndex = 1; colIndex <= 5; colIndex++) { // Only columns A to E
      const cell = worksheet.getCell(rowIndex, colIndex);
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    }
  }

  // Explicitly hide all columns beyond E to prevent table expansion
  for (let colIndex = 6; colIndex <= 50; colIndex++) {
    worksheet.getColumn(colIndex).hidden = true;
  }

  // Add freeze panes to keep headers visible
  worksheet.views = [
    {
      state: "frozen",
      xSplit: 0,
      ySplit: 5, // Freeze at row 5 (header row)
      topLeftCell: "A6",
      activeCell: "A6",
    },
  ];

  // Generate and return buffer
  const buffer = (await workbook.xlsx.writeBuffer()) as Buffer;
  return buffer;
}

// Helper function to export CTA analytics to Excel
async function exportCtaAnalyticsToExcel(
  data: any[],
  dateRange: string,
  organizationId: string | null | undefined,
  res: Response
): Promise<any> {
  try {
    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("CTA Analytics Export");

    // Get organization name
    const orgName = organizationId
      ? await getOrgName(organizationId)
      : "Organization";

    // Add title and metadata
    // Aggregate data to match frontend table structure
    const aggregatedData = aggregateCtaDataForExport(data);

    const titleRow = worksheet.addRow([
      `${orgName || "Organization"} - CTA Click Analytics Export`,
    ]);
    titleRow.font = { bold: true, size: 16, color: { argb: "FF135e96" } };
    titleRow.alignment = { horizontal: "center" };
    worksheet.mergeCells("A1:E1");

    const dateRangeRow = worksheet.addRow([`Date Range: ${dateRange}`]);
    dateRangeRow.font = { bold: true, size: 12 };
    worksheet.mergeCells("A2:E2");

    const exportDateRow = worksheet.addRow([
      `Export Date: ${new Date().toLocaleString()}`,
    ]);
    exportDateRow.font = { italic: true, size: 10 };
    worksheet.mergeCells("A3:E3");

    const totalRow = worksheet.addRow([`Total Records: ${aggregatedData.length}`]);
    totalRow.font = { bold: true, size: 12 };
    worksheet.mergeCells("A4:E4");

    worksheet.addRow([]); // Empty row

    // Add headers with styling (matching frontend table)
    const headers = [
      "ID",
      "Recipe Name",
      "CTA Type",
      "Clicks",
      "Last Clicked At",
    ];

    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF135e96" },
    };
    headerRow.alignment = { horizontal: "center", vertical: "middle" };
    headerRow.height = 25;

    // Apply header styling to each cell
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FF135e96" },
      };
      cell.alignment = { horizontal: "center", vertical: "middle" };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });

    // Add data rows
    aggregatedData.forEach((item: any, index: number) => {
      const dataRow = worksheet.addRow([
        item.id,
        item.recipe_name || "Unknown Recipe",
        item.cta_type || "Contact Info",
        item.clicks || 0,
        item.last_clicked_at ? new Date(item.last_clicked_at).toLocaleString() : "N/A",
      ]);

      // Add alternating row colors for better readability
      if (index % 2 === 0) {
        dataRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF8F9FA" },
        };
      }
    });

    // Set column widths for exactly 5 columns only
    worksheet.columns = [
      { width: 8 }, // ID
      { width: 30 }, // Recipe Name
      { width: 15 }, // CTA Type
      { width: 12 }, // Clicks
      { width: 20 }, // Last Clicked At
    ];

    // Calculate correct total rows: 4 title/header rows + data rows
    const totalDataRows = 6 + aggregatedData.length; // Row 1-4: titles, Row 5: empty, Row 6: header, Row 7+: data

    // Add borders to all cells with data (5 columns: A to E only)
    for (let rowIndex = 1; rowIndex <= totalDataRows; rowIndex++) {
      for (let colIndex = 1; colIndex <= 5; colIndex++) { // Only columns A to E
        const cell = worksheet.getCell(rowIndex, colIndex);
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      }
    }

    // Explicitly hide all columns beyond E to prevent table expansion
    for (let colIndex = 6; colIndex <= 50; colIndex++) {
      worksheet.getColumn(colIndex).hidden = true;
    }

    // Add freeze panes to keep headers visible
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 6, // Freeze at row 6 (header row)
        topLeftCell: "A7",
        activeCell: "A7",
      },
    ];

    // Add header and footer for printing
    worksheet.headerFooter.oddHeader = `&C&"Arial,Bold"&14${orgName || "Organization"} - CTA Analytics Export`;
    worksheet.headerFooter.oddFooter = `&L&"Arial"&10Generated on: ${new Date().toLocaleString()}&R&"Arial"&10Page &P of &N`;

    // Set response headers
    const filename = `cta-analytics-export-${new Date().toISOString().split("T")[0]}.xlsx`;
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Excel export error:", error);
    throw error;
  }
}

/**
 * Track recipe views for authenticated users (private API)
 * @route POST /v1/private/analytics/track/recipe-view
 */
const trackPrivateRecipeView = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name } = sanitizedBody;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
        },
      });
    }

    // Get user information from token
    const userId = req.user?.id;
    const userOrganizationId = req.user?.organization_id;

    if (!userId) {
      return res.status(401).json({
        status: false,
        message: "Authentication required",
      });
    }

    // Get recipe's organization_id and verify access
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id", "recipe_title"],
    });

    if (!recipe) {
      return res.status(404).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Check if user has default access (admin) - don't count admin views
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Only track views for non-admin users
    if (!hasDefaultAccess) {
      // Track recipe view with user information
      const metadata: any = {
        recipe_name: recipe_name || recipe.recipe_title,
        timestamp: new Date().toISOString(),
        tracking_source: "authenticated_private",
        user_organization_id: userOrganizationId,
      };

      // Use existing trackEvent method
      await analyticsService.trackEvent({
        eventType: AnalyticsEventType.RECIPE_VIEW,
        entityType: AnalyticsEntityType.RECIPE,
        entityId: recipeIdNumber,
        organizationId: recipe.organization_id,
        userId: userId,
        ipAddress: req.ip,
        userAgent: req.get("User-Agent"),
        metadata: metadata,
      });

      // Also increment the recipe_impression field
      await Recipe.increment("recipe_impression", {
        where: { id: recipeIdNumber },
      });
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: hasDefaultAccess
        ? "Recipe view acknowledged (admin view not tracked)"
        : "Recipe view tracked successfully",
      admin_view: hasDefaultAccess,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error tracking private recipe view"
    );
  }
};

export default {
  // Public analytics (simplified)
  trackCtaClick,
  submitContactForm,
  trackRecipeView,
  trackPrivateRecipeView,

  // Dashboard analytics (simplified)
  getCtaClickAnalytics,
  getContactSubmissionAnalytics,
  exportContactSubmissions,
  exportCtaAnalytics,
  deleteContactSubmission,
  bulkDeleteContactSubmissions,

  // Recipe view statistics endpoints
  getRecipeViewStatistics,
  resetRecipeViewStatistics,

  // Activity feed
  getActivityFeed,
};
