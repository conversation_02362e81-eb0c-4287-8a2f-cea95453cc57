openapi: 3.0.3
info:
  title: Recipe Management API
  description: |
    Comprehensive Recipe Management System API with file upload capabilities.

    ## Features
    - Complete CRUD operations for Categories, Food Attributes, Recipe Measures
    - Advanced file upload system with S3/MinIO integration
    - Multi-tenant organization support
    - Role-based access control
    - Comprehensive search and filtering

    ## Authentication
    All private endpoints require Bearer token authentication.

    ## File Upload
    - Supports multiple file formats (images, videos, documents)
    - Automatic duplicate detection using SHA-256 hashing
    - Organization-based file isolation
    - S3/MinIO cloud storage integration

  version: 1.0.0
  contact:
    name: Recipe API Support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8028/api/v1
    description: Development server
  - url: https://staging.namastevillage.theeasyaccess.com/api/v1
    description: Staging server

tags:
  - name: Categories
    description: Recipe and ingredient category management
  - name: Food Attributes
    description: Nutrition, allergen, cuisine, and dietary attributes
  - name: Recipe Measures
    description: Measurement units for recipes
  - name: File Upload
    description: File upload operations with S3/MinIO integration

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    # Base Response Schemas
    SuccessResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        status:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        error:
          type: object

security:
  - BearerAuth: []

paths:
  # Category Management APIs
  /private/category/list:
    get:
      tags:
        - Categories
      summary: Get all categories
      description: Retrieve all categories with filtering, searching, and pagination
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: search
          in: query
          description: Search term for category name or description
          required: false
          schema:
            type: string
        - name: category_type
          in: query
          description: Filter by category type
          required: false
          schema:
            type: string
            enum: [recipe, ingredient]
        - name: category_status
          in: query
          description: Filter by category status
          required: false
          schema:
            type: string
            enum: [active, inactive]
      responses:
        '200':
          description: Categories retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Categories retrieved successfully"
                  data:
                    type: object
                    properties:
                      categories:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 1
                            category_name:
                              type: string
                              example: "Italian Cuisine"
                            category_slug:
                              type: string
                              example: "italian-cuisine"
                            category_description:
                              type: string
                              example: "Traditional Italian recipes"
                            category_status:
                              type: string
                              enum: [active, inactive]
                              example: "active"
                            category_type:
                              type: string
                              enum: [recipe, ingredient]
                              example: "recipe"
        '401':
          description: Unauthorized
        '500':
          description: Internal server error