/**
 * Helper functions for batch processing recipe data
 */

/**
 * Calculate batch information for recipe steps
 * @param totalSteps Total number of steps
 * @param batchSize Number of steps per batch
 */
export const calculateStepBatches = (totalSteps: number, batchSize: number = 5) => {
  const totalBatches = Math.ceil(totalSteps / batchSize);

  return {
    totalBatches,
    batchSize,
    totalSteps
  };
};

/**
 * Generate batch arrays from a large array
 * @param items Array of items to split into batches
 * @param batchSize Number of items per batch
 */
export const splitIntoBatches = <T>(items: T[], batchSize: number = 5): T[][] => {
  const batches: T[][] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }

  return batches;
};

/**
 * Generate progress information for batch uploads
 * @param currentBatch Current batch number
 * @param totalBatches Total number of batches
 * @param itemsInBatch Number of items in current batch
 * @param totalItems Total number of items across all batches
 */
export const generateBatchProgress = (
  currentBatch: number,
  totalBatches: number,
  itemsInBatch: number,
  totalItems: number
) => {
  return {
    currentBatch,
    totalBatches,
    itemsInBatch,
    totalItems,
    percentComplete: Math.round((currentBatch / totalBatches) * 100),
    isComplete: currentBatch === totalBatches
  };
};
