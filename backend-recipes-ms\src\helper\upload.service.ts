import multer from "multer";
import path from "path";
import crypto from "crypto";
import {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getHash, RECIPE_FILE_UPLOAD_CONSTANT } from "./common";

// Import Item model and enums directly
import {
  Item,
  item_external_location,
  item_IEC,
  item_status,
  item_type,
} from "../models/Item";

// Setup MinIO client
const s3 = new S3Client({
  endpoint: global.config.MINIO_ENDPOINT,
  region: "us-east-1",
  forcePathStyle: true,
  credentials: {
    accessKeyId: global.config.MINIO_ACCESS_KEY,
    secretAccessKey: global.config.MINIO_SECRET_KEY,
  },
});

/**
 * Determine the correct folder path based on field name and context
 * @param fieldName - The form field name (e.g., 'categoryIcon', 'attributeIcon', 'unitIcon')
 * @param fileName - The clean file name
 * @param req - The request object to get context
 * @param entityId - Optional entity ID (recipeId, ingredientId, etc.)
 * @param stepNumber - Optional step number for instruction media
 * @returns The folder path for the file
 */
const determineFolderPath = (
  fieldName: string,
  fileName: string,
  req: any,
  entityId?: any,
  stepNumber?: number
): string => {
  const isSystemDefault = !req.user?.organization_id;
  const orgName = isSystemDefault ? null : req.user?.organization_id;

  // Map field names to upload constants
  switch (fieldName) {
    // Category, Attribute, Unit Icons (no entity ID needed)
    case "categoryIcon":
      return RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
        orgName,
        null,
        fileName
      );
    case "attributeIcon":
      return RECIPE_FILE_UPLOAD_CONSTANT.ATTRIBUTE_ICON.destinationPath(
        orgName,
        null,
        fileName
      );
    case "unitIcon":
      return RECIPE_FILE_UPLOAD_CONSTANT.UNIT_ICON.destinationPath(
        orgName,
        null,
        fileName
      );

    // Ingredient files
    case "ingredientIcon":
    case "ingredientImage":
      return RECIPE_FILE_UPLOAD_CONSTANT.INGREDIENT_IMAGE.destinationPath(
        orgName,
        entityId,
        fileName
      );

    // Recipe files - support multiple types
    case "recipeIcon":
    case "recipeImage":
    case "recipeImages":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "recipeVideo":
    case "recipeVideos":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_VIDEO.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "recipeDocument":
    case "recipeDocuments":
    case "recipePdf":
    case "recipePdfs":
    case "recipeFile":
    case "recipeFiles":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "recipeAudio":
    case "recipeAudios":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_AUDIO.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "recipeThumbnail":
    case "recipeThumbnails":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_THUMBNAIL.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "recipeInstructionMedia":
    case "stepMedia":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(
        orgName,
        entityId,
        stepNumber || 1,
        fileName
      );
    case "nutritionLabel":
    case "nutritionLabels":
      return RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_NUTRITION_LABELS.destinationPath(
        orgName,
        entityId,
        fileName
      );

    default: {
      // Fallback for unknown field names
      const fallbackPath = isSystemDefault
        ? "recipe_defaults/recipe_misc"
        : `${orgName}/recipe_misc`;
      return `${fallbackPath}/${fileName}`;
    }
  }
};

/**
 * Auto-detect field name based on file MIME type for recipe uploads
 * @param mimeType - The file MIME type
 * @param originalFieldName - The original field name from the form
 * @returns Enhanced field name for proper routing
 */
const enhanceFieldNameByMimeType = (
  mimeType: string,
  originalFieldName: string
): string => {
  // If field name already specifies the type, use it as-is
  if (
    originalFieldName.includes("Video") ||
    originalFieldName.includes("Document") ||
    originalFieldName.includes("Image") ||
    originalFieldName.includes("Pdf")
  ) {
    return originalFieldName;
  }

  // Auto-detect based on MIME type for generic field names
  if (originalFieldName.startsWith("recipe")) {
    if (mimeType.startsWith("video/")) {
      return "recipeVideo";
    } else if (mimeType.startsWith("audio/")) {
      return "recipeAudio";
    } else if (mimeType === "application/pdf") {
      return "recipeDocument";
    } else if (
      mimeType.startsWith("application/") ||
      mimeType.startsWith("text/")
    ) {
      return "recipeDocument";
    } else if (mimeType.startsWith("image/")) {
      return "recipeImage";
    }
  }

  // Return original field name if no enhancement needed
  return originalFieldName;
};

/**
 * S3 upload service with hash verification to prevent duplicates
 * @param bucketName - The S3 bucket name
 * @param folderPath - Optional folder path within the bucket
 * @returns multer instance configured for S3 storage
 */
const multerS3 = (bucketName: string, folderPath: string = "") => {
  try {
    // Use memory storage for direct S3 upload (no local files)
    const storage = multer.memoryStorage();
    const upload = multer({
      storage,
      fileFilter: (req: any, file: any, cb: any) => {
        // Basic file type validation
        const allowedMimes = [
          // Images
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/webp",
          "image/svg+xml",
          // Videos
          "video/mp4",
          "video/webm",
          "video/avi",
          "video/mov",
          "video/quicktime",
          // Audio
          "audio/mp3",
          "audio/mpeg",
          "audio/wav",
          "audio/ogg",
          "audio/aac",
          "audio/m4a",
          // Documents
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "text/plain",
        ];

        if (allowedMimes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error(`File type ${file.mimetype} is not allowed`));
        }
      },
    });

    createBucketIfNotExists(bucketName).then().catch();

    // Custom middleware for single field with single files
    const s3UploadMiddleware = (fieldName: string, maxCount: number = 1) => {
      const multerMiddleware = upload.array(fieldName, maxCount);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files || req.files?.length === 0) {
            return next();
          }

          const uploadedFiles: any[] = [];

          try {
            for (const file of req.files) {
              // Generate file hash to check for duplicates
              const fileHash = crypto
                .createHash("md5")
                .update(file.buffer)
                .digest("hex");

              // Use clean filename without timestamp
              const cleanFileName = file.originalname.replace(
                /[^a-zA-Z0-9.-]/g,
                "_"
              );
              const fileName = cleanFileName;

              // Enhance field name based on MIME type for better routing
              const enhancedFieldName = enhanceFieldNameByMimeType(
                file.mimetype,
                fieldName
              );

              // Determine the correct file path using the helper function
              const filePath = determineFolderPath(
                enhancedFieldName,
                fileName,
                req
              );




              const fileBuffer = file.buffer;

              // Check if file already exists in the bucket
              let fileExists = false;
              let fileExistAtLocation = false;
              try {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });
                if (getItem && getItem.id) {
                  fileExists = true;
                  const checkS3FileExist = await s3.send(
                    new GetObjectCommand({
                      Bucket: bucketName,
                      Key: getItem?.item_location,
                    })
                  );
                  if (checkS3FileExist && checkS3FileExist?.Body) {
                    fileExistAtLocation = true;
                  }
                }
              } catch (error) {
                // File doesn't exist, continue with upload
              }

              if (!fileExists) {
                // Upload file to S3
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );

                const saveItem: any = {
                  item_type:
                    file.mimetype == "multipart/form-data"
                      ? item_type.VIDEO
                      : file.mimetype == "application/octet-stream"
                        ? item_type.VIDEO
                        : file.mimetype.split("/")[0] == "application"
                          ? "pdf"
                          : file.mimetype.split("/")[0],
                  item_name: file.originalname,
                  item_hash: fileHash,
                  item_mime_type: file.mimetype,
                  item_extension: path.extname(file.originalname),
                  item_size: file.size,
                  item_IEC: item_IEC.B,
                  item_status: item_status.ACTIVE,
                  item_external_location: item_external_location.NO,
                  item_location: filePath,
                  item_organization_id: req.user?.organization_id || null,
                  created_by: req.user?.id || null,
                  updated_by: req.user?.id || null,
                };

                const item = await Item.create(saveItem);

                // Add file info to the request
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: item.id,
                  type: item.item_type,
                  isMovable: true,
                });
              } else {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });
                if (!fileExistAtLocation) {
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );
                  await Item.update(
                    {
                      item_location: filePath,
                      item_status: item_status?.ACTIVE,
                    },
                    {
                      where: {
                        id: getItem?.id,
                      },
                    }
                  );

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: true,
                  });
                } else {
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: getItem?.item_location,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: false,
                  });
                }
              }
            }

            // Organize files by field name for proper access
            req.files = { [fieldName]: uploadedFiles };
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    // Custom middleware for single field with multiple files
    const s3UploadArrayMiddleware = (
      fieldName: string,
      maxCount: number = 10
    ) => {
      const multerMiddleware = upload.array(fieldName, maxCount);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            console.error("Multer error:", err);
            return next(err);
          }

          if (!req.files || req.files?.length === 0) {
            console.log("No files found in request");
            return next();
          }

          console.log(`Processing ${req.files.length} files`);
          const uploadedFiles: any[] = [];

          try {
            for (const file of req.files) {
              console.log("Processing file:", file.originalname);

              // Generate file hash to check for duplicates
              const fileHash = crypto
                .createHash("md5")
                .update(file.buffer)
                .digest("hex");

              console.log("File hash:", fileHash);

              // Use clean filename without timestamp
              const cleanFileName = file.originalname.replace(
                /[^a-zA-Z0-9.-]/g,
                "_"
              );
              const fileName = cleanFileName;

              // Enhance field name based on MIME type for better routing
              const enhancedFieldName = enhanceFieldNameByMimeType(
                file.mimetype,
                fieldName
              );

              // Determine the correct file path using the helper function
              const filePath = determineFolderPath(
                enhancedFieldName,
                fileName,
                req
              );

              console.log("File path:", filePath);

              const fileBuffer = file.buffer;

              let fileExists = false;
              let fileExistAtLocation = false;
              try {
                console.log("Checking for existing file with hash:", fileHash);
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });

                if (getItem && getItem.id) {
                  console.log("Found existing item:", getItem.id);
                  fileExists = true;
                  const checkS3FileExist = await s3.send(
                    new GetObjectCommand({
                      Bucket: bucketName,
                      Key: getItem?.item_location,
                    })
                  );
                  if (checkS3FileExist && checkS3FileExist?.Body) {
                    console.log("File exists at S3 location");
                    fileExistAtLocation = true;
                  }
                } else {
                  console.log("No existing item found");
                }
              } catch (error) {
                console.error("Error checking for existing file:", error);
                // File doesn't exist, continue with upload
              }

              if (!fileExists) {
                console.log("Uploading new file to S3");
                // Upload file to S3
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );

                const saveItem: any = {
                  item_type:
                    file.mimetype == "multipart/form-data"
                      ? item_type.VIDEO
                      : file.mimetype == "application/octet-stream"
                        ? item_type.VIDEO
                        : file.mimetype.split("/")[0] == "application"
                          ? "pdf"
                          : file.mimetype.split("/")[0],
                  item_name: file.originalname,
                  item_hash: fileHash,
                  item_mime_type: file.mimetype,
                  item_extension: path.extname(file.originalname),
                  item_size: file.size,
                  item_IEC: item_IEC.B,
                  item_status: item_status.ACTIVE,
                  item_external_location: item_external_location.NO,
                  item_location: filePath,
                  item_organization_id: req.user?.organization_id || null,
                  created_by: req.user?.id || null,
                  updated_by: req.user?.id || null,
                };

                console.log("Creating item in database:", saveItem);
                const item = await Item.create(saveItem);
                console.log("Item created with ID:", item.id);

                // Add file info to the request
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: item.id,
                  type: item.item_type,
                  isMovable: true,
                });
              } else {
                console.log("File already exists, checking location");
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });
                if (!fileExistAtLocation) {
                  console.log("File doesn't exist at S3 location, uploading");
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );
                  await Item.update(
                    {
                      item_location: filePath,
                      item_status: item_status?.ACTIVE,
                    },
                    {
                      where: {
                        id: getItem?.id,
                      },
                    }
                  );

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: true,
                  });
                } else {
                  console.log("File exists at S3 location, using existing");
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: getItem?.item_location,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: false,
                  });
                }
              }
            }

            console.log("Final uploadedFiles:", uploadedFiles);
            // Replace the files array with our processed files
            req.files = uploadedFiles;
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    // Custom middleware for multiple fields
    const s3UploadFieldsMiddleware = (
      fields: { name: string; maxCount?: number }[]
    ) => {
      const multerMiddleware = upload.fields(fields);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files) {
            return next();
          }

          try {
            // Process each field separately
            const processedFiles: any = {};

            for (const fieldName of Object.keys(req.files)) {
              const fieldFiles = req.files[fieldName];
              const uploadedFiles: any[] = [];

              for (const file of fieldFiles) {
                // Generate file hash to check for duplicates
                const fileHash = crypto
                  .createHash("md5")
                  .update(file.buffer)
                  .digest("hex");

                // Use clean filename without timestamp
                const cleanFileName = file.originalname.replace(
                  /[^a-zA-Z0-9.-]/g,
                  "_"
                );
                const fileName = cleanFileName;

                // Enhance field name based on MIME type for better routing
                const enhancedFieldName = enhanceFieldNameByMimeType(
                  file.mimetype,
                  fieldName
                );

                // Determine the correct file path using the helper function
                const filePath = determineFolderPath(
                  enhancedFieldName,
                  fileName,
                  req
                );


                const fileBuffer = file.buffer;

                let fileExists = false;
                let fileExistAtLocation = false;
                try {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash,
                      item_organization_id: req.user?.organization_id,
                    },
                  });
                  if (getItem && getItem.id) {
                    fileExists = true;
                    const checkS3FileExist = await s3.send(
                      new GetObjectCommand({
                        Bucket: bucketName,
                        Key: getItem?.item_location,
                      })
                    );
                    if (checkS3FileExist && checkS3FileExist?.Body) {
                      fileExistAtLocation = true;
                    }
                  }
                } catch {
                  // File doesn't exist, continue with upload
                }

                if (!fileExists) {
                  // Upload file to S3
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );

                  const saveItem: any = {
                    item_type:
                      file.mimetype == "multipart/form-data"
                        ? item_type.VIDEO
                        : file.mimetype == "application/octet-stream"
                          ? item_type.VIDEO
                          : file.mimetype.split("/")[0] == "application"
                            ? "pdf"
                            : file.mimetype.split("/")[0],
                    item_name: file.originalname,
                    item_hash: fileHash,
                    item_mime_type: file.mimetype,
                    item_extension: path.extname(file.originalname),
                    item_size: file.size,
                    item_IEC: item_IEC.B,
                    item_status: item_status.ACTIVE,
                    item_external_location: item_external_location.NO,
                    item_location: filePath,
                    item_organization_id: req.user?.organization_id || null,
                    created_by: req.user?.id || null,
                    updated_by: req.user?.id || null,
                  };

                  const item = await Item.create(saveItem);

                  // Add file info to the request
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: item.id,
                    type: item.item_type,
                    isMovable: true,
                  });
                } else {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash,
                      item_organization_id: req.user?.organization_id,
                    },
                  });
                  if (!fileExistAtLocation) {
                    await s3.send(
                      new PutObjectCommand({
                        Bucket: bucketName,
                        Key: filePath,
                        Body: fileBuffer,
                        ContentType: file.mimetype,
                      })
                    );
                    await Item.update(
                      {
                        item_location: filePath,
                        item_status: item_status?.ACTIVE,
                      },
                      {
                        where: {
                          id: getItem?.id,
                        },
                      }
                    );

                    uploadedFiles.push({
                      originalname: file.originalname,
                      filename: fileName,
                      path: filePath,
                      size: file.size,
                      mimetype: file.mimetype,
                      hash: fileHash,
                      bucket: bucketName,
                      item_id: getItem.id,
                      type: getItem?.item_type,
                      isMovable: true,
                    });
                  } else {
                    uploadedFiles.push({
                      originalname: file.originalname,
                      filename: fileName,
                      path: getItem?.item_location,
                      size: file.size,
                      mimetype: file.mimetype,
                      hash: fileHash,
                      bucket: bucketName,
                      item_id: getItem.id,
                      type: getItem?.item_type,
                      isMovable: false,
                    });
                  }
                }
              }

              // Add processed files for this field
              processedFiles[fieldName] = uploadedFiles;
            }

            // Replace the files object with our processed files
            req.files = processedFiles;
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    // Single file upload middleware (for .upload() method)
    const s3SingleUploadMiddleware = (fieldName: string) => {
      const multerMiddleware = upload.single(fieldName);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            console.error("Multer error:", err);
            return next(err);
          }



          if (!req.file) {

            return next();
          }

          const uploadedFiles: any[] = [];

          try {
            const file = req.file;

            // Generate file hash to check for duplicates
            const fileHash = crypto
              .createHash("md5")
              .update(file.buffer)
              .digest("hex");

            // Use clean filename without timestamp
            const cleanFileName = file.originalname.replace(
              /[^a-zA-Z0-9.-]/g,
              "_"
            );
            const fileName = cleanFileName;

            // Enhance field name based on MIME type for better routing
            const enhancedFieldName = enhanceFieldNameByMimeType(
              file.mimetype,
              fieldName
            );

            // Determine the correct file path using the helper function
            const filePath = determineFolderPath(
              enhancedFieldName,
              fileName,
              req
            );

            const fileBuffer = file.buffer;

            // Check if file already exists in the bucket
            let fileExists = false;
            let fileExistAtLocation = false;
            try {
              const getItem: any = await Item.findOne({
                where: {
                  item_hash: fileHash,
                  item_organization_id: req.user?.organization_id,
                },
              });
              if (getItem && getItem.id) {
                fileExists = true;
                const checkS3FileExist = await s3.send(
                  new GetObjectCommand({
                    Bucket: bucketName,
                    Key: getItem?.item_location,
                  })
                );
                if (checkS3FileExist && checkS3FileExist?.Body) {
                  fileExistAtLocation = true;
                }
              }
            } catch {
              // File doesn't exist, continue with upload
            }

            if (!fileExists) {

              // Upload file to S3
              await s3.send(
                new PutObjectCommand({
                  Bucket: bucketName,
                  Key: filePath,
                  Body: fileBuffer,
                  ContentType: file.mimetype,
                })
              );

              const saveItem: any = {
                item_type:
                  file.mimetype == "multipart/form-data"
                    ? item_type.VIDEO
                    : file.mimetype == "application/octet-stream"
                      ? item_type.VIDEO
                      : file.mimetype.split("/")[0] == "application"
                        ? "pdf"
                        : file.mimetype.split("/")[0],
                item_name: file.originalname,
                item_hash: fileHash,
                item_mime_type: file.mimetype,
                item_extension: path.extname(file.originalname),
                item_size: file.size,
                item_IEC: item_IEC.B,
                item_status: item_status.ACTIVE,
                item_external_location: item_external_location.NO,
                item_location: filePath,
                item_organization_id: req.user?.organization_id || null,
                created_by: req.user?.id || null,
                updated_by: req.user?.id || null,
              };

              const item = await Item.create(saveItem);


              // Add file info to the request
              uploadedFiles.push({
                originalname: file.originalname,
                filename: fileName,
                path: filePath,
                size: file.size,
                mimetype: file.mimetype,
                hash: fileHash,
                bucket: bucketName,
                item_id: item.id,
                type: item.item_type,
                isMovable: true,
              });
            } else {

              const getItem: any = await Item.findOne({
                where: {
                  item_hash: fileHash,
                  item_organization_id: req.user?.organization_id,
                },
              });

              if (!fileExistAtLocation) {
                // File exists in DB but not in S3, re-upload
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );
                await Item.update(
                  {
                    item_location: filePath,
                    item_status: item_status?.ACTIVE,
                  },
                  {
                    where: {
                      id: getItem?.id,
                    },
                  }
                );

                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: getItem.id,
                  type: getItem?.item_type,
                  isMovable: true,
                });
              } else {
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: getItem?.item_location,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: getItem.id,
                  type: getItem?.item_type,
                  isMovable: false,
                });
              }
            }

            // Organize files by field name for proper access
            req.files = { [fieldName]: uploadedFiles };

            return next();
          } catch (uploadError) {
            console.error("Error uploading to S3:", uploadError);
            return next(uploadError);
          }
        });
      };
    };

    return {
      upload: s3SingleUploadMiddleware,
      fields: s3UploadFieldsMiddleware,
      array: s3UploadArrayMiddleware,
    };
  } catch (error) {
    console.error("S3 upload configuration error:", error);
    // Return dummy middleware functions to prevent TypeScript errors
    const errorMiddleware = (_req: any, _res: any, next: any) => {

      return next(new Error("S3 upload configuration failed"));
    };

    return {
      upload: (_fieldName: string, _maxCount: number = 1) => errorMiddleware,
      fields: (_fields: { name: string; maxCount?: number }[]) =>
        errorMiddleware,
      array: (_fieldName: string, _maxCount: number = 10) => errorMiddleware,
    };
  }
};

const createBucketIfNotExists = async (bucketName: string) => {
  try {
    await s3.send(new HeadBucketCommand({ Bucket: bucketName }));
    // Bucket already exists
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      try {
        await s3.send(new CreateBucketCommand({ Bucket: bucketName }));
        console.log(`Bucket ${bucketName} created successfully`);
      } catch (createError: any) {
        console.error(`Error creating bucket: ${createError.message}`);
      }
    } else {
      console.error(`Error checking bucket: ${error.message}`);
    }
  }
};

/**
 * Move a file to a different path within the same bucket
 * @param bucketName - The bucket name
 * @param sourceKey - Current file path/key
 * @param destinationKey - New file path/key
 * @param deleteSource - Whether to delete the source file after copying (default: true)
 * @returns Object containing information about the move operation
 */
const moveFileInBucket = async (
  bucketName: string,
  sourceKey: string,
  destinationKey: string,
  item_id: number,
  deleteSource: boolean = true
): Promise<{
  success: boolean;
  sourceUrl?: string;
  destinationUrl?: string;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Check if source file exists
    try {
      await s3.send(
        new HeadObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        })
      );
    } catch (error) {
      return {
        success: false,
        error: `Source file does not exist: ${bucketName}/${sourceKey}`,
      };
    }

    // Check if source and destination are the same
    if (sourceKey === destinationKey) {

      return {
        success: true,
        sourceUrl: `${sourceKey}`,
        destinationUrl: `${destinationKey}`,
      };
    }

    await Item.update(
      {
        item_location: destinationKey,
      },
      {
        where: {
          id: item_id,
        },
      }
    );

    // Copy the file to the destination path
    await s3.send(
      new CopyObjectCommand({
        CopySource: `${bucketName}/${sourceKey}`,
        Bucket: bucketName,
        Key: destinationKey,
      })
    );

    // Delete the source file if deleteSource is true
    if (deleteSource) {
      await s3.send(
        new DeleteObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        })
      );
    }

    return {
      success: true,
      sourceUrl: `${sourceKey}`,
      destinationUrl: `${destinationKey}`,
    };
  } catch (error) {
    console.error("Error moving file within bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

const deleteFileFromBucket = async (bucketName: string, sourceKey: string) => {
  try {
    await s3.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: sourceKey,
      })
    );
    return {
      success: true,
    };
  } catch (error) {
    console.error("Error delete file from bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

const moveFilesLocalToS3 = async (
  file: any,
  folderPath: string,
  _fileName: string,
  organization_id: string,
  bucketName: string = process.env.NODE_ENV!
) => {
  try {
    // Generate file hash to check for duplicates
    const fileHash: any = getHash(file);
    const fileName = file.originalname;
    const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

    // // Check if file already exists in the bucket
    // let fileExists = false;
    // try {
    //   if (fileHash.status) {
    //     const getItem: any = await Item.findOne({
    //       where: {
    //         item_hash: fileHash?.hash,
    //         item_organization_id: organization_id
    //       },
    //     });
    //     if (getItem && getItem.id) {
    //       fileExists = true;
    //     }
    //   }
    // } catch (error) {
    //   // File doesn't exist, continue with upload
    // }

    // Upload file to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: filePath,
        Body: file.buffer,
        ContentType: file.mimetype,
      })
    );

    const saveItem: any = {
      item_type:
        file.mimetype == "multipart/form-data"
          ? item_type.VIDEO
          : file.mimetype == "application/octet-stream"
            ? item_type.VIDEO
            : file.mimetype.split("/")[0] == "application"
              ? "pdf"
              : file.mimetype.split("/")[0],
      item_name: file.filename,
      item_hash: fileHash?.hash,
      item_mime_type: file.mimetype,
      item_extension: path.extname(file.originalname),
      item_size: file.size,
      item_IEC: item_IEC.B,
      item_status: item_status.ACTIVE,
      item_external_location: item_external_location.NO,
      item_location: filePath,
      item_organization_id: organization_id,
    };

    const item = await Item.create(saveItem);
    return {
      success: true,
      data: item,
    };
  } catch (error) {

    return {
      success: false,
      error: error,
    };
  }
};

/**
 * Upload a file buffer directly to S3
 * @param bucketName - S3 bucket name
 * @param key - The file path/key within the bucket
 * @param fileBuffer - The file data buffer
 * @param contentType - The file MIME type
 * @returns Object containing upload status and error if any
 */
const uploadFileToBucket = async (
  bucketName: string,
  key: string,
  fileBuffer: Buffer,
  contentType: string = "application/octet-stream"
): Promise<{
  success: boolean;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Upload to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
      })
    );

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error uploading file to bucket:", error);
    return {
      success: false,
      error,
    };
  }
};

// Default export object
export default {
  s3,
  multerS3,
  moveFileInBucket,
  deleteFileFromBucket,
  moveFilesLocalToS3,
  uploadFileToBucket,
};
