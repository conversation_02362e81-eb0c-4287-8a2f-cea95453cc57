import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum AnalyticsEventType {
  RECIPE_VIEW = "recipe_view",
  CTA_CLICK = "cta_click",
  CONTACT_FORM_SUBMIT = "contact_form_submit",
}

export enum AnalyticsEntityType {
  RECIPE = "recipe",
}

interface AnalyticsAttributes {
  id?: number;
  event_type: AnalyticsEventType;
  entity_type: AnalyticsEntityType;
  entity_id?: number;
  organization_id?: string;
  user_id?: number;
  ip_address?: string;
  user_agent?: string;
  metadata?: any; // JSON field for flexible data storage
  created_at?: Date;
}

export class Analytics
  extends Model<AnalyticsAttributes, never>
  implements AnalyticsAttributes
{
  id!: number;
  event_type!: AnalyticsEventType;
  entity_type!: AnalyticsEntityType;
  entity_id?: number;
  organization_id?: string;
  user_id?: number;
  ip_address?: string;
  user_agent?: string;
  metadata?: any;
  created_at!: Date;

  // Helper method to get typed metadata
  getMetadata<T = any>(): T {
    return this.metadata
      ? typeof this.metadata === "string"
        ? JSON.parse(this.metadata)
        : this.metadata
      : ({} as T);
  }

  // Static method to track events easily
  static async trackEvent(data: {
    event_type: AnalyticsEventType;
    entity_type: AnalyticsEntityType;
    entity_id?: number;
    organization_id?: string;
    user_id?: number;
    ip_address?: string;
    user_agent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    return await Analytics.create({
      ...data,
      metadata: data.metadata ? JSON.stringify(data.metadata) : null,
    });
  }
}

Analytics.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    event_type: {
      type: DataTypes.ENUM(Object.values(AnalyticsEventType)),
      allowNull: false,
    },
    entity_type: {
      type: DataTypes.ENUM(Object.values(AnalyticsEntityType)),
      allowNull: false,
    },
    entity_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ip_address: {
      type: DataTypes.STRING(45), // IPv6 support
      allowNull: true,
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: "Analytics",
    tableName: "mo_recipe_analytics",
    timestamps: false,
  }
);

export default Analytics;
