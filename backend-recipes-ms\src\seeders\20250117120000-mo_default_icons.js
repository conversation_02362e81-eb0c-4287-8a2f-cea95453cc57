"use strict";

// Register ts-node to allow requiring TypeScript files from seeders (skip type checking)
require("ts-node/register/transpile-only");
require("dotenv").config();
const projectRoot = require("path").resolve(__dirname, "../..");
// Fix env detection: default to 'staging' (matches CLI --env) and propagate to process.env
const env = process.env.NODE_ENV || "abc";

// Initialise global configuration objects early so that models can bootstrap without errors
if (!global.db) {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const dbJson = require(
    require("path").join(projectRoot, "shared/config/db.json")
  );
  global.db = dbJson[env];
}

if (!global.config) {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const cfgJson = require(
    require("path").join(projectRoot, "shared/config/config.json")
  );
  global.config = cfgJson[env] || {};
}

// Ensure Sequelize bootstrap check passes
if (!global.config.use_env_variable) global.config.use_env_variable = undefined;

const path = require("path");
const { QueryTypes } = require("sequelize");

// ---------------------------------------------------------------------
// Monkey-patch Sequelize Model association methods to NO-OP to avoid
// "belongsTo called with something that's not a subclass" warnings when
// models are loaded in isolation within this standalone seeder.
// ---------------------------------------------------------------------
const { Model } = require("sequelize");
["belongsTo", "hasOne", "hasMany", "belongsToMany"].forEach((method) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore – dynamic monkey-patching for this script only
  Model[method] = () => undefined;
});

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    console.log("🎨 Starting Default Icons Seeder...");
    console.log("📋 This seeder will upload default icons for:");
    console.log("   • Recipe Categories");
    console.log("   • Ingredient Categories");
    console.log("   • Food Attributes (Allergens)");
    console.log("");

    try {
      // Check if icons are already seeded by counting items with specific categories
      const iconCheck = await queryInterface.sequelize.query(
        `SELECT 
          SUM(CASE WHEN item_category = 'category_icon' THEN 1 ELSE 0 END) as category_icons,
          SUM(CASE WHEN item_category = 'attribute_icon' THEN 1 ELSE 0 END) as attribute_icons,
          COUNT(*) as total_icons
         FROM nv_items 
         WHERE item_organization_id IS NULL 
         AND item_category IN ('category_icon', 'attribute_icon')
         AND item_status = 'active'`,
        { type: QueryTypes.SELECT }
      );

      const existingIcons = iconCheck[0] || {
        category_icons: 0,
        attribute_icons: 0,
        total_icons: 0,
      };

      console.log(`🔍 Current Icon Status:`);
      console.log(`   Category Icons: ${existingIcons.category_icons}`);
      console.log(`   Attribute Icons: ${existingIcons.attribute_icons}`);
      console.log(`   Total System Icons: ${existingIcons.total_icons}`);
      console.log("");

      // If significant number of icons already exist, check if user wants to re-run
      if (existingIcons.total_icons >= 20) {
        console.log("⚠️  Significant number of icons already exist.");
        console.log("   To force re-upload icons, use: FORCE_ICON_UPLOAD=true");

        const forceUpload = process.env.FORCE_ICON_UPLOAD === "true";
        if (!forceUpload) {
          console.log("⏭️  Skipping icon upload (icons already exist)");
          console.log("✅ Icon seeder completed (no changes made)");
          return;
        } else {
          console.log(
            "🔄 Force flag detected - proceeding with icon upload..."
          );
        }
      }

      // BEFORE importing the service make sure we resolve the path relative to project root
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const iconSeederModule = require(
        path.resolve(__dirname, "../services/iconSeeder.service")
      );
      const iconSeeder = iconSeederModule.default || iconSeederModule;

      if (
        !iconSeeder ||
        typeof iconSeeder.uploadAllDefaultIcons !== "function"
      ) {
        throw new Error(
          "IconSeeder service not found or invalid. Please ensure iconSeeder.service.ts is properly configured."
        );
      }

      console.log("🚀 Starting icon upload process...");
      console.log("");

      // Upload all default icons using the service
      const uploadResult = await iconSeeder.uploadAllDefaultIcons(
        process.env.FORCE_ICON_UPLOAD === "true"
      );

      // Process and display results
      console.log("");
      console.log("📊 Upload Summary:");
      console.log(`   Total Items: ${uploadResult.summary.totalCategories}`);
      console.log(`   ✅ Successful: ${uploadResult.summary.totalSuccess}`);
      console.log(`   ⏭️  Skipped: ${uploadResult.summary.totalSkipped}`);
      console.log(`   ❌ Failed: ${uploadResult.summary.totalFailed}`);
      console.log("");

      // Detailed results by category
      uploadResult.details.forEach((categoryResult) => {
        console.log(`📁 ${categoryResult.category}:`);
        console.log(
          `   Total: ${categoryResult.total} | Success: ${categoryResult.success} | Skipped: ${categoryResult.skipped} | Failed: ${categoryResult.failed}`
        );

        // Show failures if any
        const failures = categoryResult.results.filter(
          (r) => !r.result.success
        );
        if (failures.length > 0) {
          console.log("   ❌ Failures:");
          failures.forEach((failure) => {
            console.log(
              `      • ${failure.name}: ${failure.result.error || "Unknown error"}`
            );
          });
        }
      });

      // Verify final state
      const finalCheck = await queryInterface.sequelize.query(
        `SELECT 
          SUM(CASE WHEN item_category = 'category_icon' THEN 1 ELSE 0 END) as category_icons,
          SUM(CASE WHEN item_category = 'attribute_icon' THEN 1 ELSE 0 END) as attribute_icons,
          COUNT(*) as total_icons
         FROM nv_items 
         WHERE item_organization_id IS NULL 
         AND item_category IN ('category_icon', 'attribute_icon')
         AND item_status = 'active'`,
        { type: QueryTypes.SELECT }
      );

      const finalIcons = finalCheck[0] || {
        category_icons: 0,
        attribute_icons: 0,
        total_icons: 0,
      };

      console.log("");
      console.log(`📈 Final Icon Status:`);
      console.log(`   Category Icons: ${finalIcons.category_icons}`);
      console.log(`   Attribute Icons: ${finalIcons.attribute_icons}`);
      console.log(`   Total System Icons: ${finalIcons.total_icons}`);

      // Report overall status
      if (uploadResult.summary.totalFailed === 0) {
        console.log("");
        console.log("🎉 All icons uploaded successfully!");
      } else if (uploadResult.summary.totalSuccess > 0) {
        console.log("");
        console.log(
          "⚠️  Some icons failed to upload. Check logs above for details."
        );
        console.log(
          "💡 You can re-run with FORCE_ICON_UPLOAD=true to retry failed uploads."
        );
      } else {
        throw new Error(
          "All icon uploads failed. Please check your configuration and try again."
        );
      }

      console.log("");
      console.log("✅ Default Icons Seeder completed successfully!");
      console.log("");
    } catch (error) {
      console.error("");
      console.error("❌ Error in Default Icons Seeder:", error.message);
      console.error("");
      console.error("🔧 Troubleshooting:");
      console.error("   1. Ensure AWS S3 credentials are configured");
      console.error(
        "   2. Check that icon files exist in src/icons/ directories"
      );
      console.error("   3. Verify database connection and permissions");
      console.error(
        "   4. Check that iconSeeder.service.ts is properly configured"
      );
      console.error("");
      console.error("💡 For detailed debugging, check the full error:");
      console.error(error);
      throw error;
    }
  },

  async down(queryInterface) {
    console.log("🗑️  Rolling back Default Icons Seeder...");
    console.log(
      "⚠️  This will remove all system icons for categories and attributes!"
    );
    console.log("");

    try {
      // Get count before deletion
      const beforeCheck = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as total FROM nv_items 
         WHERE item_organization_id IS NULL 
         AND item_category IN ('category_icon', 'attribute_icon')
         AND item_status = 'active'`,
        { type: QueryTypes.SELECT }
      );

      const beforeCount = beforeCheck[0]?.total || 0;
      console.log(`📊 Found ${beforeCount} system icons to remove`);

      if (beforeCount === 0) {
        console.log("✅ No system icons found - rollback complete");
        return;
      }

      // First, unlink icons from categories
      await queryInterface.sequelize.query(
        `UPDATE mo_category 
         SET category_icon = NULL 
         WHERE organization_id IS NULL 
         AND is_system_category = true 
         AND category_icon IS NOT NULL`,
        { type: QueryTypes.UPDATE }
      );

      // Then, unlink icons from food attributes
      await queryInterface.sequelize.query(
        `UPDATE mo_food_attributes 
         SET attribute_icon = NULL 
         WHERE organization_id IS NULL 
         AND is_system_attribute = true 
         AND attribute_icon IS NOT NULL`,
        { type: QueryTypes.UPDATE }
      );

      // Finally, remove the icon items themselves
      const deleteResult = await queryInterface.sequelize.query(
        `DELETE FROM nv_items 
         WHERE item_organization_id IS NULL 
         AND item_category IN ('category_icon', 'attribute_icon')`,
        { type: QueryTypes.DELETE }
      );

      console.log("");
      console.log("🔧 Rollback Actions Completed:");
      console.log("   ✅ Unlinked icons from categories");
      console.log("   ✅ Unlinked icons from food attributes");
      console.log("   ✅ Removed icon items from database");

      // Verify deletion
      const afterCheck = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as total FROM nv_items 
         WHERE item_organization_id IS NULL 
         AND item_category IN ('category_icon', 'attribute_icon')`,
        { type: QueryTypes.SELECT }
      );

      const afterCount = afterCheck[0]?.total || 0;
      const removedCount = beforeCount - afterCount;

      console.log("");
      console.log(`📈 Rollback Summary:`);
      console.log(`   Icons Before: ${beforeCount}`);
      console.log(`   Icons After: ${afterCount}`);
      console.log(`   Icons Removed: ${removedCount}`);

      if (afterCount === 0) {
        console.log("");
        console.log("✅ Default Icons rollback completed successfully!");
        console.log("💡 All system icons have been removed from the database");
      } else {
        console.log("");
        console.log("⚠️  Some icons may still exist - please check manually");
      }

      console.log("");
      console.log("📝 Note: This rollback only removes database records.");
      console.log(
        "   Icon files in S3 storage are preserved for data integrity."
      );
      console.log("   To re-upload icons, run the seeder again.");
    } catch (error) {
      console.error("");
      console.error("❌ Error during Default Icons rollback:", error.message);
      console.error("");
      console.error("🔧 Manual Cleanup Required:");
      console.error(
        "   1. Check mo_category table - set category_icon = NULL for system categories"
      );
      console.error(
        "   2. Check mo_food_attributes table - set attribute_icon = NULL for system attributes"
      );
      console.error(
        "   3. Check nv_items table - remove items with category_icon/attribute_icon categories"
      );
      console.error("");
      throw error;
    }
  },
};
