const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if cooking/preparation method attributes already exist
    const existingMethods = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type IN ('ingredient_cooking_method', 'preparation_method')`,
      { type: QueryTypes.SELECT }
    );

    if (existingMethods.length === 0) {
      // Cooking Method Attributes
      const cookingMethods = [
        {
          name: "Boiled",
          slug: "boiled",
          description: "Cooked in boiling water or liquid",
          type: "ingredient_cooking_method",
        },
        {
          name: "Deep Fried",
          slug: "deep-fried",
          description: "Completely submerged and cooked in hot oil",
          type: "ingredient_cooking_method",
        },
        {
          name: "Grilled",
          slug: "grilled",
          description:
            "Cooked over direct heat, typically on a grill or barbecue",
          type: "ingredient_cooking_method",
        },
        {
          name: "Microwaved",
          slug: "microwaved",
          description: "Cooked using microwave radiation",
          type: "ingredient_cooking_method",
        },
        {
          name: "Poached",
          slug: "poached",
          description: "Gently cooked in simmering liquid",
          type: "ingredient_cooking_method",
        },
        {
          name: "Roasted",
          slug: "roasted",
          description:
            "Cooked in an oven at high temperature, typically for larger cuts",
          type: "ingredient_cooking_method",
        },
        {
          name: "Shallow Fried",
          slug: "shallow-fried",
          description: "Cooked in a pan with a small amount of oil",
          type: "ingredient_cooking_method",
        },
        {
          name: "Steamed",
          slug: "steamed",
          description: "Cooked using steam from boiling water",
          type: "ingredient_cooking_method",
        },
      ];

      // Preparation Method Attributes
      const preparationMethods = [
        {
          name: "Chopped",
          slug: "chopped",
          description: "Cut into irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Crumble",
          slug: "crumble",
          description: "Broken into small irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Crushed",
          slug: "crushed",
          description: "Broken down by applying pressure",
          type: "preparation_method",
        },
        {
          name: "Cut into large chunks",
          slug: "cut-into-large-chunks",
          description: "Cut into large irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Cut into matchsticks",
          slug: "cut-into-matchsticks",
          description: "Cut into thin, matchstick-like strips",
          type: "preparation_method",
        },
        {
          name: "Cut into small chunks",
          slug: "cut-into-small-chunks",
          description: "Cut into small irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Cut into small cubes",
          slug: "cut-into-small-cubes",
          description: "Cut into small uniform cube shapes",
          type: "preparation_method",
        },
        {
          name: "Cut into wedges",
          slug: "cut-into-wedges",
          description: "Cut into triangular wedge shapes",
          type: "preparation_method",
        },
        {
          name: "Deseeded and diced",
          slug: "deseeded-and-diced",
          description: "Seeds removed and cut into small uniform cubes",
          type: "preparation_method",
        },
        {
          name: "Deseeded and sliced",
          slug: "deseeded-and-sliced",
          description: "Seeds removed and cut into thin, flat pieces",
          type: "preparation_method",
        },
        {
          name: "Diced",
          slug: "diced",
          description: "Cut into small uniform cubes",
          type: "preparation_method",
        },
        {
          name: "Finely diced",
          slug: "finely-diced",
          description: "Cut into very small uniform cubes",
          type: "preparation_method",
        },
        {
          name: "Finely sliced",
          slug: "finely-sliced",
          description: "Cut into very thin, flat pieces",
          type: "preparation_method",
        },
        {
          name: "Grated",
          slug: "grated",
          description: "Shredded using a grater",
          type: "preparation_method",
        },
        {
          name: "Minced",
          slug: "minced",
          description: "Cut into very fine pieces",
          type: "preparation_method",
        },
        {
          name: "Peeled",
          slug: "peeled",
          description: "Outer skin or layer removed",
          type: "preparation_method",
        },
        {
          name: "Puree",
          slug: "puree",
          description: "Blended into a smooth consistency",
          type: "preparation_method",
        },
        {
          name: "Roughly chopped",
          slug: "roughly-chopped",
          description: "Cut into large irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Shredded",
          slug: "shredded",
          description: "Cut or torn into thin strips",
          type: "preparation_method",
        },
        {
          name: "Sliced",
          slug: "sliced",
          description: "Cut into thin, flat pieces",
          type: "preparation_method",
        },
        {
          name: "Slice in half",
          slug: "slice-in-half",
          description: "Cut into two equal halves",
          type: "preparation_method",
        },
        {
          name: "Tear",
          slug: "tear",
          description: "Pulled apart by hand into irregular pieces",
          type: "preparation_method",
        },
        {
          name: "Zest",
          slug: "zest",
          description: "Outer peel grated or scraped off",
          type: "preparation_method",
        },
      ];

      // Combine all methods
      const allMethods = [...cookingMethods, ...preparationMethods];

      // Prepare bulk insert data
      const methodData = allMethods.map((method) => ({
        attribute_title: method.name,
        attribute_slug: method.slug,
        attribute_description: method.description,
        attribute_type: method.type,
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("mo_food_attributes", methodData);
      console.log(
        "✅ Cooking & Preparation Method Attributes seeded successfully"
      );
      console.log(
        "ℹ️  Note: Cooking & preparation method icons are not available yet - consider adding icons to src/icons/cooking_methods/"
      );
    } else {
      console.log(
        "⏭️  Cooking & Preparation Method Attributes already exist, skipping..."
      );
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: ["ingredient_cooking_method", "preparation_method"],
    });
  },
};
