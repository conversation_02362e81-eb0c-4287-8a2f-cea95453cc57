import express from "express";
import uploadService from "../../helper/upload.service";
import foodAttributesController from "../../controller/foodAttributes.controller";
import foodAttributeValidator from "../../validators/foodAttributes.validator";
import { RECIPE_FILE_UPLOAD_CONSTANT } from "../../helper/common";

const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  RECIPE_FILE_UPLOAD_CONSTANT.ATTRIBUTE_ICON.folder
);

const router = express.Router();

/**
 * Food Attributes CRUD Operations with Integrated File Upload
 * @route /api/v1/private/food-attributes
 */

/**
 * @swagger
 * /private/food-attributes/list:
 *   get:
 *     tags:
 *       - Food Attributes
 *     summary: Get all food attributes
 *     description: Retrieve all food attributes grouped by type (nutrition, allergen, cuisine, dietary) with filtering and pagination
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for attribute title or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: attribute_type
 *         in: query
 *         description: Filter by attribute type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [nutrition, allergen, cuisine, dietary]
 *       - name: attribute_status
 *         in: query
 *         description: Filter by attribute status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *       - name: hasIcon
 *         in: query
 *         description: Filter by presence of icon
 *         required: false
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: sort
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [attribute_title, attribute_type, attribute_status, created_at, updated_at]
 *           default: attribute_title
 *       - name: order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Food attributes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attributes retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     nutrition:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FoodAttribute'
 *                     allergen:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FoodAttribute'
 *                     cuisine:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FoodAttribute'
 *                     dietary:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FoodAttribute'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/list", foodAttributesController.getAllFoodAttributesByType);

/**
 * @swagger
 * /private/food-attributes/get/{id}:
 *   get:
 *     tags:
 *       - Food Attributes
 *     summary: Get food attribute by ID
 *     description: Retrieve a single food attribute by its ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Food attribute ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Food attribute retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attribute retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/FoodAttribute'
 *       404:
 *         description: Food attribute not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/get/:id",
  foodAttributeValidator.getFoodAttributeValidator(),
  foodAttributesController.getFoodAttributeById
);

/**
 * @swagger
 * /private/food-attributes/create:
 *   post:
 *     tags:
 *       - Food Attributes
 *     summary: Create a new food attribute
 *     description: Create a new food attribute with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - attribute_title
 *               - attribute_type
 *             properties:
 *               attribute_title:
 *                 type: string
 *                 example: "Protein"
 *                 minLength: 2
 *                 maxLength: 100
 *               attribute_description:
 *                 type: string
 *                 example: "High protein content"
 *               attribute_type:
 *                 type: string
 *                 enum: [nutrition, allergen, cuisine, dietary]
 *                 example: "nutrition"
 *               attribute_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               attributeIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Attribute icon image file"
 *     responses:
 *       201:
 *         description: Food attribute created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attribute created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/FoodAttribute'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create",
  multerS3Upload.upload("attributeIcon"),
  foodAttributeValidator.createFoodAttributeValidator(),
  foodAttributesController.createFoodAttribute
);

/**
 * @swagger
 * /private/food-attributes/update/{id}:
 *   put:
 *     tags:
 *       - Food Attributes
 *     summary: Update food attribute
 *     description: Update an existing food attribute with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Food attribute ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               attribute_title:
 *                 type: string
 *                 example: "Updated Protein"
 *                 minLength: 2
 *                 maxLength: 100
 *               attribute_description:
 *                 type: string
 *                 example: "Updated high protein content"
 *               attribute_type:
 *                 type: string
 *                 enum: [nutrition, allergen, cuisine, dietary]
 *                 example: "nutrition"
 *               attribute_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               attributeIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Attribute icon image file"
 *     responses:
 *       200:
 *         description: Food attribute updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attribute updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/FoodAttribute'
 *       404:
 *         description: Food attribute not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update/:id",
  multerS3Upload.upload("attributeIcon"),
  foodAttributeValidator.updateFoodAttributeValidator(),
  foodAttributesController.updateFoodAttribute
);

/**
 * @swagger
 * /private/food-attributes/delete/{id}:
 *   delete:
 *     tags:
 *       - Food Attributes
 *     summary: Delete food attribute
 *     description: Soft delete a food attribute by ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Food attribute ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Food attribute deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attribute deleted successfully"
 *       404:
 *         description: Food attribute not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/delete/:id",
  foodAttributeValidator.deleteFoodAttributeValidator(),
  foodAttributesController.deleteFoodAttribute
);

export default router;
