import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum AttributeStatus {
  active = "active",
  inactive = "inactive",
}

export enum AttributeType {
  nutrition = "nutrition",
  allergen = "allergen",
  cuisine = "cuisine",
  dietary = "dietary",
  ingredient_cooking_method = "ingredient_cooking_method",
  haccp_category = "haccp_category",
  preparation_method = "preparation_method",
}

interface FoodAttributesAttributes {
  id?: number;
  attribute_title: string;
  attribute_slug: string;
  attribute_description?: string;
  attribute_icon?: number;
  attribute_status: AttributeStatus;
  organization_id?: string;
  attribute_type: AttributeType;
  is_system_attribute: boolean;
  created_by: number;
  updated_by: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class FoodAttributes
  extends Model<FoodAttributesAttributes, never>
  implements FoodAttributesAttributes {
  id!: number;
  attribute_title!: string;
  attribute_slug!: string;
  attribute_description?: string;
  attribute_icon?: number;
  attribute_status!: AttributeStatus;
  organization_id?: string;
  attribute_type!: AttributeType;
  is_system_attribute!: boolean;
  created_by!: number;
  updated_by!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

FoodAttributes.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    attribute_title: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    attribute_slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    attribute_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    attribute_icon: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Foreign key reference to nv_items table for attribute icon",
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    attribute_status: {
      type: DataTypes.ENUM(Object.values(AttributeStatus)),
      allowNull: false,
      defaultValue: AttributeStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    attribute_type: {
      type: DataTypes.ENUM(Object.values(AttributeType)),
      allowNull: false,
    },
    is_system_attribute: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_food_attributes",
    modelName: "FoodAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["attribute_slug", "organization_id", "attribute_type"],
        name: "unique_attribute_slug_per_org_type",
      },
      {
        fields: ["organization_id"],
        name: "idx_attribute_organization",
      },
      {
        fields: ["attribute_type"],
        name: "idx_attribute_type",
      },
      {
        fields: ["attribute_status"],
        name: "idx_attribute_status",
      },
      {
        fields: ["created_by"],
        name: "idx_attribute_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_attribute_updated_by",
      },
    ],
  },
);

// Define associations
FoodAttributes.associate = (models: any) => {
  // FoodAttributes belongs to User (created_by)
  FoodAttributes.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // FoodAttributes belongs to User (updated_by)
  FoodAttributes.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  FoodAttributes.belongsTo(models.Item, {
    foreignKey: "attribute_icon",
    as: "iconItem",
    constraints: true, //
    onDelete: "SET NULL", //
    onUpdate: "CASCADE", //
  });

  // Many-to-many association with Ingredient through IngredientAttributes
  FoodAttributes.belongsToMany(models.Ingredient, {
    through: models.IngredientAttributes,
    foreignKey: "attributes_id",
    otherKey: "ingredient_id",
    as: "ingredients",
  });

  // Many-to-many association with Recipe through RecipeAttributes
  FoodAttributes.belongsToMany(models.Recipe, {
    through: models.RecipeAttributes,
    foreignKey: "attributes_id",
    otherKey: "recipe_id",
    as: "recipes",
  });
};

export default FoodAttributes;
