const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if nutrition attributes already exist
    const existingNutrition = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type = 'nutrition'`,
      { type: QueryTypes.SELECT }
    );

    if (existingNutrition.length === 0) {
      // Nutrition Attributes
      const nutritionAttributes = [
        {
          name: "Energy",
          slug: "energy",
          description: "Amount of energy provided by the food item"
        },
        {
          name: "Fat",
          slug: "fat",
          description: "Total fat content in the food item"
        },
        {
          name: "Saturates",
          slug: "saturates",
          description: "Amount of saturated fats present"
        },
        {
          name: "Carbohydrate",
          slug: "carbohydrate",
          description: "Total carbohydrate content including sugars"
        },
        {
          name: "Sugars",
          slug: "sugars",
          description: "Amount of sugar present within total carbohydrates"
        },
        {
          name: "Fibre",
          slug: "fibre",
          description: "Dietary fibre content in the food item"
        },
        {
          name: "Protein",
          slug: "protein",
          description: "Protein content contributing to muscle and tissue repair"
        },
        {
          name: "Salt",
          slug: "salt",
          description: "Amount of salt (sodium) present in the food"
        }
      ];

      // Prepare bulk insert data
      const nutritionData = nutritionAttributes.map(nutrition => ({
        attribute_title: nutrition.name,
        attribute_slug: nutrition.slug,
        attribute_description: nutrition.description,
        attribute_type: "nutrition",
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert("mo_food_attributes", nutritionData);
      console.log("✅ Nutrition Attributes seeded successfully");
    } else {
      console.log("⏭️  Nutrition Attributes already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: "nutrition"
    });
  }
};
