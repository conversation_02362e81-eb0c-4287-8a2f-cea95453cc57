import express from "express";
import foodAttributesController from "../../controller/foodAttributes.controller";
import foodAttributeValidator from "../../validators/foodAttributes.validator";

const router = express.Router();

/**
 * @swagger
 * /public/food-attributes:
 *   get:
 *     tags:
 *       - Public Food Attributes
 *     summary: Get all active food attributes
 *     description: Retrieve all active food attributes grouped by type (nutrition, allergen, cuisine, dietary) with filtering and pagination. No authentication required.
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for attribute title or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: type
 *         in: query
 *         description: Filter by attribute type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [nutrition, allergen, cuisine, dietary]
 *       - name: hasIcon
 *         in: query
 *         description: Filter attributes by icon presence
 *         required: false
 *         schema:
 *           type: boolean
 *       - name: sort
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [attribute_title, attribute_type, created_at, updated_at]
 *           default: attribute_title
 *       - name: order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Food attributes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Food attributes fetched successfully"
 *                 count:
 *                   type: integer
 *                   example: 45
 *                 data:
 *                   type: object
 *                   properties:
 *                     nutrition:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           attribute_title:
 *                             type: string
 *                             example: "Protein"
 *                           attribute_description:
 *                             type: string
 *                             example: "Essential macronutrient for muscle building"
 *                           attribute_type:
 *                             type: string
 *                             example: "nutrition"
 *                           attribute_status:
 *                             type: string
 *                             enum: [active]
 *                             example: "active"
 *                           attribute_slug:
 *                             type: string
 *                             example: "protein"
 *                           iconUrl:
 *                             type: string
 *                             nullable: true
 *                             example: "https://example.com/icons/protein.png"
 *                           hasIcon:
 *                             type: boolean
 *                             example: true
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                     allergen:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 2
 *                           attribute_title:
 *                             type: string
 *                             example: "Gluten"
 *                           attribute_description:
 *                             type: string
 *                             example: "Protein found in wheat and other grains"
 *                           attribute_type:
 *                             type: string
 *                             example: "allergen"
 *                           attribute_status:
 *                             type: string
 *                             enum: [active]
 *                             example: "active"
 *                           attribute_slug:
 *                             type: string
 *                             example: "gluten"
 *                           iconUrl:
 *                             type: string
 *                             nullable: true
 *                             example: "https://example.com/icons/gluten.png"
 *                           hasIcon:
 *                             type: boolean
 *                             example: true
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                     cuisine:
 *                       type: array
 *                       items:
 *                         type: object
 *                     dietary:
 *                       type: array
 *                       items:
 *                         type: object
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 10
 *                 total_pages:
 *                   type: integer
 *                   example: 5
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation failed"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Invalid attribute type"]
 *       429:
 *         description: Too many requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Rate limit exceeded"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get(
  "/list",
  foodAttributeValidator.getFoodAttributesListValidator(),
  (req: any, res: any, next: any) => {
    // For public API, simulate a user with default access to bypass organization restrictions
    req.user = {
      id: null,
      organization_id: null,
      roles: [{ role_name: 'public' }]
    };
    // Force status to 'active' for public API
    req.query.status = 'active';
    // Remove any organization filters for public access
    delete req.query.organizationId;
    delete req.query.isSystemAttribute;
    next();
  },
  foodAttributesController.getAllFoodAttributesByType
);


export default router;
