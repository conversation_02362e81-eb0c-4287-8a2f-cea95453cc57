const ExcelJS = require('exceljs');
const fs = require('fs');

// Test data similar to what would come from the database
const testData = [
  {
    id: 1,
    recipe_name: "AAAAA",
    cta_type: "Contact Info",
    clicks: 2,
    last_clicked_at: "2025-07-14T07:15:31.163Z"
  },
  {
    id: 2,
    recipe_name: "Pasta Paneer recipe",
    cta_type: "Contact Info",
    clicks: 2,
    last_clicked_at: "2025-07-14T12:14:15.000Z"
  },
  {
    id: 3,
    recipe_name: "AAAAA",
    cta_type: "Contact Info",
    clicks: 1,
    last_clicked_at: "2025-07-14T04:38:59.000Z"
  }
];

async function testExcelExport() {
  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("CTA Analytics");

  // Add export information
  const titleRow = worksheet.addRow([
    `CTA Click Analytics Export - ${new Date().toISOString()}`,
  ]);
  titleRow.font = { bold: true, size: 14 };
  titleRow.alignment = { horizontal: "center" };
  worksheet.mergeCells("A1:E1");

  const dateRangeRow = worksheet.addRow([`Date Range: last_30_days`]);
  dateRangeRow.font = { bold: true, size: 12 };
  worksheet.mergeCells("A2:E2");

  const totalRow = worksheet.addRow([`Total Records: ${testData.length}`]);
  totalRow.font = { bold: true, size: 12 };
  worksheet.mergeCells("A3:E3");

  worksheet.addRow([]); // Empty row

  // Add headers with styling (matching frontend table)
  const headers = [
    "ID",
    "Recipe Name",
    "CTA Type",
    "Clicks",
    "Last Clicked At",
  ];

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FF135e96" },
  };
  headerRow.alignment = { horizontal: "center", vertical: "middle" };
  headerRow.height = 25;

  // Add data rows
  testData.forEach((item, index) => {
    const dataRow = worksheet.addRow([
      item.id,
      item.recipe_name || "Unknown Recipe",
      item.cta_type || "Contact Info",
      item.clicks || 0,
      item.last_clicked_at ? new Date(item.last_clicked_at).toLocaleString() : "N/A",
    ]);

    // Add alternating row colors for better readability
    if (index % 2 === 0) {
      dataRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF8F9FA" },
      };
    }
  });

  // Set column widths for exactly 5 columns only
  worksheet.columns = [
    { width: 8 }, // ID
    { width: 30 }, // Recipe Name
    { width: 15 }, // CTA Type
    { width: 12 }, // Clicks
    { width: 20 }, // Last Clicked At
  ];

  // Calculate correct total rows: 3 title rows + 1 empty + 1 header + data rows
  const totalDataRows = 5 + testData.length; // Row 1-3: titles, Row 4: empty, Row 5: header, Row 6+: data

  // Add borders to all cells with data (5 columns: A to E only)
  for (let rowIndex = 1; rowIndex <= totalDataRows; rowIndex++) {
    for (let colIndex = 1; colIndex <= 5; colIndex++) { // Only columns A to E
      const cell = worksheet.getCell(rowIndex, colIndex);
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    }
  }

  // Explicitly hide all columns beyond E to prevent table expansion
  for (let colIndex = 6; colIndex <= 50; colIndex++) {
    worksheet.getColumn(colIndex).hidden = true;
  }

  // Add freeze panes to keep headers visible
  worksheet.views = [
    {
      state: "frozen",
      xSplit: 0,
      ySplit: 5, // Freeze at row 5 (header row)
      topLeftCell: "A6",
      activeCell: "A6",
    },
  ];

  // Generate and save buffer
  const buffer = await workbook.xlsx.writeBuffer();
  fs.writeFileSync('test-cta-analytics-export.xlsx', buffer);
  console.log('Excel file generated: test-cta-analytics-export.xlsx');
}

testExcelExport().catch(console.error);
