import express, { Router } from "express";
import contactRoute from "./contact.routes";
import analyticsRoute from "./analytics.routes";
import recipeRoute from "./recipe.routes";
import categoriesRoute from "./categories.routes";
import foodAttributesRoute from "./food-attributes.routes";
import ingredientsRoute from "./ingredients.routes";

const routes: Router = express.Router();

// Contact Us routes (public - no authentication required)
routes.use("/contact-us", contactRoute);

// Analytics routes (public - no authentication required)
routes.use("/analytics", analyticsRoute);
// Recipe routes (public - no authentication required)
routes.use("/recipes", recipeRoute);

// category routes (public - no authentication required)
routes.use("/category", categoriesRoute);

// Food attributes routes (public - no authentication required)
routes.use("/food-attributes", foodAttributesRoute);

// Ingredients routes (public - no authentication required)
routes.use("/ingredients", ingredientsRoute);

export default routes;
