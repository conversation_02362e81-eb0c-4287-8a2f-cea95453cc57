import express from "express";
import uploadService from "../../helper/upload.service";
import categoryController from "../../controller/category.controller";
import categoryValidator from "../../validators/category.validator";
import { RECIPE_FILE_UPLOAD_CONSTANT } from "../../helper/common";

const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.folder
);

const router = express.Router();

/**
 * @swagger
 * /private/category/list:
 *   get:
 *     tags:
 *       - Categories
 *     summary: Get all categories
 *     description: Retrieve all categories with advanced filtering, searching, and pagination
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for category name or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by category status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *       - name: type
 *         in: query
 *         description: Filter by category type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [recipe, ingredient]
 *       - name: categoryUse
 *         in: query
 *         description: Alias for type parameter
 *         required: false
 *         schema:
 *           type: string
 *           enum: [recipe, ingredient]
 *       - name: hasIcon
 *         in: query
 *         description: Filter by presence of icon
 *         required: false
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: sort
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [category_name, category_description, category_status, category_type, created_at, updated_at]
 *           default: category_name
 *       - name: order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Categories retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Category'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/list", categoryController.getAllCategories);

/**
 * @swagger
 * /private/category/get/{id}:
 *   get:
 *     tags:
 *       - Categories
 *     summary: Get category by ID
 *     description: Retrieve a single category by its ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Category ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Category retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Category retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Category'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/get/:id",
  categoryValidator.getCategoryValidator(),
  categoryController.getCategoryById
);

/**
 * @swagger
 * /private/category/create:
 *   post:
 *     tags:
 *       - Categories
 *     summary: Create a new category
 *     description: Create a new category with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - category_name
 *               - category_type
 *             properties:
 *               category_name:
 *                 type: string
 *                 example: "Vegetables"
 *                 minLength: 2
 *                 maxLength: 100
 *               category_description:
 *                 type: string
 *                 example: "Fresh vegetables and greens"
 *               category_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               category_type:
 *                 type: string
 *                 enum: [recipe, ingredient]
 *                 example: "ingredient"
 *               categoryIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Category icon image file"
 *     responses:
 *       201:
 *         description: Category created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Category created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Category'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create",
  multerS3Upload.upload("categoryIcon"),
  categoryValidator.createCategoryValidator(),
  categoryController.createCategory
);

/**
 * @swagger
 * /private/category/update/{id}:
 *   put:
 *     tags:
 *       - Categories
 *     summary: Update category
 *     description: Update an existing category with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Category ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               category_name:
 *                 type: string
 *                 example: "Updated Vegetables"
 *                 minLength: 2
 *                 maxLength: 100
 *               category_description:
 *                 type: string
 *                 example: "Updated fresh vegetables and greens"
 *               category_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               category_type:
 *                 type: string
 *                 enum: [recipe, ingredient]
 *                 example: "ingredient"
 *               categoryIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Category icon image file"
 *     responses:
 *       200:
 *         description: Category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Category updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Category'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update/:id",
  multerS3Upload.upload("categoryIcon"),
  categoryValidator.updateCategoryValidator(),
  categoryController.updateCategory
);

/**
 * @swagger
 * /private/category/delete/{id}:
 *   delete:
 *     tags:
 *       - Categories
 *     summary: Delete category
 *     description: Soft delete a category by ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Category ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Category deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Category deleted successfully"
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/delete/:id",
  categoryValidator.deleteCategoryValidator(),
  categoryController.deleteCategory
);

export default router;
