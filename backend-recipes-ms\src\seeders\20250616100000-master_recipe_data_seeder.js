const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    console.log("🌱 Starting Recipe Management System Data Seeding...");

    try {
      // Check if master seeding has already been completed
      const masterCheck = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as total FROM (
          SELECT COUNT(*) as count FROM mo_category WHERE organization_id IS NULL AND is_system_category = true
          UNION ALL
          SELECT COUNT(*) as count FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true
          UNION ALL
          SELECT COUNT(*) as count FROM mo_recipe_measure WHERE organization_id IS NULL AND is_system_unit = true
        ) as combined`,
        { type: QueryTypes.SELECT }
      );

      const totalSystemData = masterCheck[0]?.total || 0;

      if (totalSystemData > 0) {
        console.log("⏭️  System data already exists, skipping master seeding...");
        console.log("📊 Use individual seeders to add specific data types if needed.");
        return;
      }

      console.log("📋 Seeding Order:");
      console.log("   1. Categories (Recipe & Ingredient)");
      console.log("   2. Food Attributes (Allergens & Dietary)");
      console.log("   3. Cuisine Attributes");
      console.log("   4. Recipe Measurement Units");
      console.log("   5. Nutrition Attributes");
      console.log("   6. HACCP Category Attributes");
      console.log("   7. Cooking & Preparation Methods");
      console.log("");

      // Note: Individual seeders will handle their own existence checks
      // This master seeder provides overview and coordination

      console.log("✅ Master seeding preparation complete!");
      console.log("🚀 Run individual seeders or 'npm run seeder:all' to populate data");
      console.log("");
      console.log("📝 Available Commands:");
      console.log("   npm run seeder:all              - Run all seeders");
      console.log("   npx sequelize-cli db:seed:all   - Alternative command");
      console.log("");
      console.log("🔍 Individual Seeder Files:");
      console.log("   20250616100001-mo_categories.js");
      console.log("   20250616100002-mo_food_attributes.js");
      console.log("   20250616100003-mo_cuisine_attributes.js");
      console.log("   20250616100004-mo_recipe_measure.js");
      console.log("   20250616100005-mo_nutrition_attributes.js");
      console.log("   20250616100006-mo_haccp_attributes.js");
      console.log("   20250616100007-mo_cooking_preparation_methods.js");

    } catch (error) {
      console.error("❌ Error in master seeder:", error);
      throw error;
    }
  },

  async down(queryInterface) {
    console.log("🗑️  Rolling back all Recipe Management System data...");

    try {
      // Remove all system data in reverse order
      await queryInterface.bulkDelete("mo_food_attributes", {
        organization_id: null,
        is_system_attribute: true
      });

      await queryInterface.bulkDelete("mo_recipe_measure", {
        organization_id: null,
        is_system_unit: true
      });

      await queryInterface.bulkDelete("mo_category", {
        organization_id: null,
        is_system_category: true
      });

      console.log("✅ All system data removed successfully");
    } catch (error) {
      console.error("❌ Error rolling back data:", error);
      throw error;
    }
  }
};
